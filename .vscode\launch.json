{"version": "0.2.0", "configurations": [{"type": "msedge", "request": "launch", "name": "localhost (Edge)", "url": "http://127.0.0.1:4200", "webRoot": "${workspaceFolder}", "sourceMapPathOverrides": {"webpack:/*": "${webRoot}/*", "/./*": "${webRoot}/*", "/src/*": "${webRoot}/*", "/*": "*", "/./~/*": "${webRoot}/node_modules/*"}}, {"type": "chrome", "request": "launch", "name": "localhost (Chrome)", "url": "http://127.0.0.1:4201", "webRoot": "${workspaceFolder}"}]}