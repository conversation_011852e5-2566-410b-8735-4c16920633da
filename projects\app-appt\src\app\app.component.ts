import { Component, HostListener, Inject, OnInit } from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { DateAdapter, MAT_DATE_LOCALE } from '@angular/material/core';
import { DatePipe } from '@angular/common';
import { BreakpointObserver } from '@angular/cdk/layout';
@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  standalone: true,
  imports: [RouterOutlet],
 
})
export class AppComponent implements OnInit {

  constructor( public dialog: MatDialog,private router: Router,
    private breakpointObserver: BreakpointObserver, ) {
    
  }
  ngOnInit(): void {
    
  }


  navigateToPatientScreen() {
    this.router.navigate(['his-psd/patients']);
  }

  @HostListener('window:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    if (event.ctrlKey && event.key === 'q') {
      event.preventDefault();
      this.navigateToPatientScreen();
    } else if (event.ctrlKey && event.key === 'y') {
      event.preventDefault();
      this.navigateToAppointmentScreen();
    } else if (event.ctrlKey && event.key === 'i') {
      event.preventDefault();
      this.navigateToVisitQueue();
    }else if (event.ctrlKey && event.key === 'b') {
      event.preventDefault();
      this.navigateToBillingQueue();
    }
  }
  
  navigateToAppointmentScreen() {
    this.router.navigate(['appointments']);
  }

  navigateToVisitQueue() {
    this.router.navigate(['his-psd/service']);
  }
  navigateToBillingQueue() {
    this.router.navigate(['his-psd/billings']);
  }
  
}

