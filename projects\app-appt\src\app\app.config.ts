import { ApplicationConfig, provideBrowserGlobalErrorListeners, provideZoneChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';

import { routes } from './app.routes';
import { provideClientHydration, withEventReplay } from '@angular/platform-browser';
import { appDefaultProviders} from 'ecmed-app';

export const appConfig: ApplicationConfig = {
  providers: [
    appDefaultProviders(),
    provideRouter(routes), 
    provideClientHydration(withEventReplay())
  ]
};
