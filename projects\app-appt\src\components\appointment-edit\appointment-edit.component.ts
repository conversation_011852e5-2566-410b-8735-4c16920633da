import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MaterialPackage} from '@app-appt/utils';

@Component({
  selector: 'his-appoitmentedit',
  standalone: true,
  imports: [CommonModule,MaterialPackage],
  templateUrl: './appointment-edit.component.html',
  styleUrl: './appointment-edit.component.scss'
})
export class AppoitmenteditComponent {

  constructor(private dialogRef: MatDialogRef<AppoitmenteditComponent>) { }

  goBack(): void {
    this.dialogRef.close();
  }
}
