<div class="h-full">
<mat-list-item class="pl-2 indivial-details">Individual Details</mat-list-item>
<mat-divider></mat-divider>
<div class="pl-2 pr-2 pt-2">
  <div class="flex gap-2 w-full">
    <mat-form-field #idTypeInput appearance="outline">
      <mat-label>ID Type</mat-label>
      <mat-select [(ngModel)]="searchtype" required>
        <mat-option *ngFor="let data of visitData?.ID" [value]="data.IDENTIFIER">{{ data.DESCRIPTION
          }}</mat-option>
      </mat-select>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>IDNO</mat-label>
      <input matInput placeholder="IDNO" (ngModelChange)="Resetvalue()" [(ngModel)]="searchQuery" required
        (keyup.enter)="onSearch()" name="IDNo" />
      <mat-icon matIconSuffix matTooltip="Individual Search, Add, Edit" class="cursor-pointer text-blue-600"
        svgIcon="person_search" (click)="handleIdDialogOpen()">
      </mat-icon>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>HRN</mat-label>
      <input matInput placeholder="IDNO" disabled required name="HRN" />
    </mat-form-field>
  </div>
  <div class="flex space-x-2 w-full">
    <p class="red-color" *ngIf="showMandatoryFieldsMessage">
      *Please fill all mandatory fields to register individuals.
    </p>
  </div>
  <div class="flex space-x-2 w-full">

    <mat-form-field appearance="outline" class="w-40">
      <mat-label>Title</mat-label>
      <mat-select [(ngModel)]="Appointment.TITLE">
        @for (data of visitData?.TC; track $index){
        <mat-option [value]="data.IDENTIFIER">
          {{ data.DESCRIPTION }}
        </mat-option>
        }
      </mat-select>
    </mat-form-field>


    <mat-form-field appearance="outline" class="w-1/2">
      <mat-label for="label">Name</mat-label>
      <input matInput type="text" [(ngModel)]="Appointment.NAME" required aria-describedby="emailHelp" maxlength="100"
        autocomplete="off" />
    </mat-form-field>
    <mat-form-field appearance="outline">
      <mat-label for="label">Gender</mat-label>
      <mat-select [(ngModel)]="Appointment.GENDER" required>
        <mat-option *ngFor="let data of visitData?.SX" [value]="data.IDENTIFIER">{{ data.DESCRIPTION
          }}</mat-option>
      </mat-select>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label>Date of Birth</mat-label>
      <input matInput [matDatepicker]="picker" [(ngModel)]="Appointment.DOB" ec-AutoFormatDate required
        (dateChange)="calculateAge(Appointment.DOB)" placeholder="MM/dd/yyyy">
      <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
      <mat-datepicker #picker></mat-datepicker>
      <mat-error class="text-xs" *ngIf="!Appointment.DOB">Date of Birth is required</mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline">
      <mat-label for="label">Age</mat-label>
      <input matInput type="number" [(ngModel)]="age" aria-describedby="emailHelp" maxlength="100" autocomplete="off"
        readonly />
    </mat-form-field>
  </div>

</div>

<mat-tab-group  ecui-height-fill>
  <mat-tab>
    <ng-template mat-tab-label class="address-icon ">
      <mat-icon svgIcon="assignment_ind" class="address-icon"></mat-icon>
      <strong class="address-icon"> Personal Profile</strong>
    </ng-template>

    <div class="p-1 h-full">
      <div class="flex space-x-2 w-full">
        <mat-form-field class="w-full" appearance="outline">
          <mat-label>Ethnicity</mat-label>
          <mat-select [(ngModel)]="Appointment.ETHNICITY">
            <mat-option *ngFor="let data of visitData?.RC" [value]="data.IDENTIFIER">{{ data.DESCRIPTION
              }}</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field class="w-full" appearance="outline">
          <mat-label>Place of Birth</mat-label>
          <mat-select [(ngModel)]="Appointment.PLACE_OF_BIRTH">
            <mat-option *ngFor="let data of visitData?.CY" [value]="data.IDENTIFIER">{{ data.DESCRIPTION
              }}</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field class="w-full" appearance="outline">
          <mat-label>Nationality</mat-label>
          <mat-select [(ngModel)]="Appointment.NATIONALITY">
            <mat-option *ngFor="let data of visitData?.NA" [value]="data.IDENTIFIER">{{ data.DESCRIPTION
              }}</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field class="w-full" appearance="outline">
          <mat-label>Religion</mat-label>
          <mat-select [(ngModel)]="Appointment.RELIGION">
            <mat-option *ngFor="let data of visitData?.RE" [value]="data.IDENTIFIER">{{ data.DESCRIPTION
              }}</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div class="flex space-x-1 w-full">

        <mat-form-field class="w-full" appearance="outline">
          <mat-label>Language Spoken</mat-label>
          <mat-select [(ngModel)]="Appointment.LANGUAGECODE">
            <mat-option *ngFor="let data of visitData?.LG" [value]="data.IDENTIFIER">{{ data.DESCRIPTION
              }}</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field class="w-full" appearance="outline">
          <mat-label>Education</mat-label>
          <mat-select [(ngModel)]="Appointment.EDUCATION">
            <mat-option *ngFor="let data of visitData?.ED" [value]="data.IDENTIFIER">{{ data.DESCRIPTION
              }}</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field class="w-full" appearance="outline">
          <mat-label>Occupation</mat-label>
          <mat-select [(ngModel)]="Appointment.OCCUPATION">
            <mat-option *ngFor="let data of visitData?.ON" [value]="data.IDENTIFIER">{{ data.DESCRIPTION
              }}</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field class="w-full" appearance="outline">
          <mat-label>Marital Status</mat-label>
          <mat-select [(ngModel)]="Appointment.MARITALSTATUS">
            <mat-option *ngFor="let data of visitData?.MS" [value]="data.IDENTIFIER">{{ data.DESCRIPTION
              }}</mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <div class="flex gap-x-1">
        <mat-form-field appearance="outline" class="w-1/3">
          <mat-label for="label">Email</mat-label>
          <input matInput type="email" [(ngModel)]="Appointment.EMAIL" name="email" #email="ngModel" required email
            aria-describedby="emailHelp" maxlength="100" autocomplete="off" />
          <mat-error *ngIf="email.invalid && email.touched">
            @if (email.invalid && email.touched){
            <span>Please enter a valid email address.</span>
            }
          </mat-error>
        </mat-form-field>
        <div class="flex space-x-2 w-full">
          <section class="example-section">
            <mat-checkbox class="example-margin" [(ngModel)]="organDonor"
              (change)="updateCheckboxValue('organDonor', $event)">Organ Donor</mat-checkbox>
            <mat-checkbox class="example-margin" [(ngModel)]="speakEnglish" (change)="
                        updateCheckboxValue('speakEnglish', $event)
                    ">Can Speak English</mat-checkbox>
          </section>

          <mat-list-item class="font-semibold pt-1.5">Alert Type :</mat-list-item>
          <section class="example-section">
            <mat-checkbox class="example-margin" [(ngModel)]="alertEmail"
              (change)="updateCheckboxValue('alertEmail', $event)">Email</mat-checkbox>
            <mat-checkbox class="example-margin" [(ngModel)]="alertSMS"
              (change)="updateCheckboxValue('alertSMS', $event)">SMS</mat-checkbox>
          </section>
        </div>
      </div>
      <form [formGroup]="myForm" class="flex gap-x-2">
        <div class="w-50">
          <ngx-material-intl-tel-input [fieldControl]="myForm.get('phoneNumber')" [autoIpLookup]="false"
            [appearance]="'outline'" [mainLabel]="''">
          </ngx-material-intl-tel-input>
          @if (myForm.get('phoneNumber')?.touched && myForm.get('phoneNumber')?.invalid){
          <div class="error-message">
            Please enter a valid number
          </div>

          }
        </div>

        <div class="h-max">
          <ngx-material-intl-tel-input [fieldControl]="myForm.get('phoneNumber2')" [autoIpLookup]="false"
            [appearance]="'outline'" [mainLabel]="''">
          </ngx-material-intl-tel-input>
          @if(myForm.get('phoneNumber2')?.touched && myForm.get('phoneNumber2')?.invalid){
          <div class="error-message">
            Please enter a valid number
          </div>

          }
        </div>

      </form>

    </div>
  </mat-tab>
  <mat-tab>
    <ng-template mat-tab-label class="address-icon">
      <mat-icon svgIcon="assignment" class="address-icon"></mat-icon>
      <strong class="address-icon">Address</strong>
    </ng-template>

    <div class="p-2 h-full">
      <his-common-address
        [addressData]="getAddressData()"
        [visitData]="visitData"
        [phoneForm]="myForm"
        (addressChange)="onAddressChange($event)">
      </his-common-address>
    </div>
  </mat-tab>
</mat-tab-group>
</div>