import { CommonModule, DatePipe, DecimalPipe } from '@angular/common';
import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { EcmedWebapiModelsPatientmgmtSearchPatientType } from 'ecmed-api/visitmgmt';
import { AbstractControl, FormControl, FormGroup, FormsModule, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { MatCheckboxChange, MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatTabsModule } from '@angular/material/tabs';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DoaService } from '@app-appt/services/doa.service';
import { TraceOnarrivalService } from '@app-appt/services/traceonarrival.service';
import { IndividualDetailDialogComponent, AutoFormatDateDirective, CommonService } from 'his-components';
import { PopupComponent } from '../popup/popup.component';
import { NgxMaterialIntlTelInputComponent } from 'ngx-material-intl-tel-input';
import { HisCommonAddressComponent } from '../his-common-address/his-common-address.component';
import { VisitDataService } from '@app-appt/enums';
@Component({
  selector: 'appt-appointmentform',
  standalone: true,
  imports: [
    CommonModule,
    AutoFormatDateDirective,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatOptionModule,
    MatIconModule,
    MatDatepickerModule,
    MatTabsModule,
    MatListModule,
    MatDividerModule,
    MatTooltipModule,
    MatCheckboxModule,
    NgxMaterialIntlTelInputComponent,
    HisCommonAddressComponent
  ],
  templateUrl: './appointment-form.component.html',
  styleUrl: './appointment-form.component.scss',
  providers: [DatePipe, DecimalPipe, FormGroupDirective
  ],
})

export class AppointmentformComponent {

  constructor(private arrivalService: DoaService,
    public datepipe: DatePipe,
    public _dialog: MatDialog,
    public traceonservice: TraceOnarrivalService,
    public commonServices: CommonService,
    private visitDataService: VisitDataService) { }

  @ViewChild('idTypeInput') idTypeInput?: ElementRef;

  myForm: FormGroup = new FormGroup({
    phoneNumber: new FormControl(''),
    phoneNumber2: new FormControl(''),
    phoneNumber3: new FormControl(''),
    phoneNumber4: new FormControl(''),
  });

  _searchResults: any = [];
  _finaldata: any;
  subscription: Subscription = new Subscription();
  DOATransactionModelData: any[] = [];
  TraceOnArrivalModelData: any[] = [];
  dialogsavelist: any = {};
  resultvalue: any = {};
  AppointNo: any;
  anotherVariable: any;
  smsalert: any;
  Appointmentdual: any;
  appointmentService: any;
  showMandatoryFieldsMessage: boolean = false;
  appSpeciality: any;
  appSubSpeciality: any;
  searchPerformed = false;
  appClinics: any;
  appResources: any;
  appResourceTypes: any;
  appDoctors: any;
  searchname: any;
  searchtype: any;
  searchQuery: any;
  doadata: any = [];
  toadata: any = [];
  Appointment: any = {
    TITLE: null
  };
  appointmentview: any;
  Appointmentselected: any;
  appointmentId?: string;
  age: any;
  organDonor: boolean = true;
  speakEnglish: boolean = false;
  alertEmail: boolean = false;
  alertSMS: boolean = false;

  @Output() saveEvent = new EventEmitter<any>();
  @Output() saveIndividual = new EventEmitter<any>();
  @Output() searchEvent = new EventEmitter<any>();
  @Output() goBack: EventEmitter<void> = new EventEmitter<void>();

  visitData: any = {};
  @Input() appoitmentvalue: any

  @Input() set finaldata(value: any) {
    this.Appointment.IDENTIFIER = value;
    this._finaldata = value;
  }

  @Input() set searchResults(value: any) {
    console.log(value, 'value for appt ind');
    if (value?.length) {
      this._searchResults = value;
      this.onSearches()
    } else {
      this._searchResults = []
      this.resetForm()
    }
  }

  ngOnInit() {
    this.subscribeToDataServices();
    this.visitDataService.fetchAllVisitData().subscribe(
      (response: any) => {
        this.visitData = response;
      }
    );
  }
  private subscribeToDataServices() {
    // Subscribe to DOA Service
    this.subscription.add(
      this.arrivalService.arrival$.subscribe(data => {
        if (data?.selectarrival) {
          
          this.DOATransactionModelData = data.selectarrival.map((item: any, index: number) => ({
            Identifier: item?.IDENTIFIER?.toString() || '',
            serialno: (index + 1).toString(),
            AppointmentId: '',
            procedureid: item?.IDENTIFIER?.toString() || ''
          }));
         this.doadata = this.DOATransactionModelData;
        }
      })
    );

    // Subscribe to Trace On Arrival Service
    this.subscription.add(
      this.traceonservice.traceonarrival$.subscribe(data => {
        if (data?.selectarrival) {
         
          this.TraceOnArrivalModelData = data.selectarrival.map((item: any, index: number) => ({
            Identifier: item?.IDENTIFIER?.toString() || '',
            serialno: (index + 1).toString(),
            AppointmentId: '',
            procedureid: item?.IDENTIFIER?.toString() || ''
          }));
          this.toadata = this.TraceOnArrivalModelData;
        }
      })
    );
  }

  ngOnDestroy() {
    console.log(this.visitData, 'what')
    this.subscription.unsubscribe();
  }
  phoneValidator(control: AbstractControl) {
    const valid = /(\+\d{1,3}[- ]?)?\d{10}/.test(control.value); // Example regex for phone number validation
    return valid ? null : { invalidPhone: true };
  }

  onBackButtonClick(): void {
    this.goBack.emit();
  }

  handleIdDialogOpen() {
    let dialogRef = this._dialog.open(IndividualDetailDialogComponent, {
      width: "900vw", height: "90vh",
      disableClose: true,
    })

    dialogRef.afterClosed().subscribe(res => {
      if (res && res.data && res.data.element) {
        this.Appointment = res?.data?.element;
        this.searchQuery = res?.data?.element?.IDNO;
        this.calculateAge(res?.data?.element?.DOB);
        this.searchtype = res?.data?.element?.IDTYPE;
        this.updatePhoneNumbersForm(res?.data?.element);
        // Bind the ALERTEMAIL and ALERTSMS values to component properties
        this.alertEmail = res?.data?.element?.ALERTEMAIL === 'Y';
        this.alertSMS = res?.data?.element?.ALERTSMS === 'Y';
        this.organDonor = res?.data?.element?.ORGANDONOR === 'Y';
        this.speakEnglish = res?.data?.element?.SPEAKENGLISH === 'Y';
      }
      else {
        // Handle the case where `res` or `res.data.element` is undefined
        this.Appointment = '';
      }
    })
  }

  onSearch() {
    const searchData = {
      SearchType: EcmedWebapiModelsPatientmgmtSearchPatientType.ByIdNo,
      Name: this.searchname,
      iDNo: this.searchQuery,
      iDType: this.searchtype
    };
    this.searchEvent.emit(searchData);
  }

  onSearches() {
    if (!this._searchResults?.length) {
      this.searchQuery = '';
      this.openConfirmDialog();
      this.showMandatoryFieldsMessage = true;
      this.resetForm();
      return;
    }

    // Get the first patient result
    const patient = this._searchResults[0];
    console.log('Patient data for form binding:', patient);

    // Update the appointment object
    this.Appointment = { ...patient };
    this.calculateAge(this.Appointment.DOB);
    this.showMandatoryFieldsMessage = false;

    // Update form with telephone numbers
    this.updatePhoneNumbersForm(patient);
  }

  private updatePhoneNumbersForm(patient: any) {
    // Extract telephone numbers with fallback to empty string
    const tel1 = patient?.TEL || '';
    const tel2 = patient?.TEL2 || '';
    const tel3 = patient?.TEL3 || '';
    const tel4 = patient?.TEL4 || '';

    console.log('Binding telephone numbers:', { tel1, tel2, tel3, tel4 });

    // Update form controls
    this.myForm.patchValue({
      phoneNumber: tel1,
      phoneNumber2: tel2,
      phoneNumber3: tel3,
      phoneNumber4: tel4
    });

    // Force change detection
    this.myForm.markAsDirty();
    this.myForm.updateValueAndValidity();
  }

  private resetForm() {
    this.myForm.patchValue({
      phoneNumber: '',
      phoneNumber2: '',
      phoneNumber3: '',
      phoneNumber4: ''
    });
    this.myForm.markAsPristine();
  }

  calculateAge(dob: any): void {
    const today = new Date();
    const birthDate = new Date(dob);
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    this.age = age;
  }

  openConfirmDialog(): void {
    const dialogRef = this._dialog.open(PopupComponent, {
      width: '550px'
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result == 'yes') {
        this.Appointment = {};
        this.age = '';
        this.idTypeInput?.nativeElement.focus();
      }
    });
  }

  handleValidate = () => {
    let formObj: any = {
      IDType: this.searchtype?.toString(),
      IDNO: this.searchQuery,
      name: this.Appointment.NAME,
      gender: this.Appointment.GENDER,
      dob: this.Appointment.DOB,
      email: this.Appointment.EMAIL,
    }, formKey = Object.keys(formObj), text = ""

    for (let i = 0; i < formKey.length; i++) {
      if (!formObj[formKey[i]]) {
        text = (formKey[i].toUpperCase() + " " + "is empty")
        break
      }
    }
    return (text)

  }

  updateCheckboxValue(field: string, event: MatCheckboxChange): void {
    let me = <any>this;
    me[field] = event.checked ? 'Y' : 'N';
  }

  Resetvalue() {
    this.Appointment = [];
    this.resetForm();
    this.searchQuery = '';
    this.showMandatoryFieldsMessage = false;
    this.age = 0;
    this._searchResults = [];
    this.TraceOnArrivalModelData = [];
    this.DOATransactionModelData = [];
    this.searchtype = '';
  }

  getAddressData() {
    return {
      ADDRESSTYPE: this.Appointment.ADDRESSTYPE,
      ADDRESS1: this.Appointment.ADDRESS1,
      ADDRESS2: this.Appointment.ADDRESS2,
      ADDRESS3: this.Appointment.ADDRESS3,
      ADDRESS4: this.Appointment.ADDRESS4,
      COUNTRY: this.Appointment.COUNTRY,
      STATE: this.Appointment.STATE,
      CITY: this.Appointment.CITY,
      POSTALCODE: this.Appointment.POSTALCODE
    };
  }

  onAddressChange(addressData: any) {
    this.Appointment.ADDRESSTYPE = addressData.ADDRESSTYPE;
    this.Appointment.ADDRESS1 = addressData.ADDRESS1;
    this.Appointment.ADDRESS2 = addressData.ADDRESS2;
    this.Appointment.ADDRESS3 = addressData.ADDRESS3;
    this.Appointment.ADDRESS4 = addressData.ADDRESS4;
    this.Appointment.COUNTRY = addressData.COUNTRY;
    this.Appointment.STATE = addressData.STATE;
    this.Appointment.CITY = addressData.CITY;
    this.Appointment.POSTALCODE = addressData.POSTALCODE;
  }

  restrictToNumbersAndPlus(event: KeyboardEvent) {
    if (event.key === 'Backspace' || event.key === 'Tab' || event.key === 'End' || event.key === 'Home' || event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
      return;
    }
    const pattern = /[0-9\+]+/;
    if (!pattern.test(event.key)) {
      event.preventDefault();
    }
  }

  public Finalize(): void {
    this.Appointment.TEL = this.myForm?.value?.phoneNumber || "";
    this.Appointment.TEL2 = this.myForm?.value?.phoneNumber2 || "";
    this.Appointment.TEL3 = this.myForm?.value?.phoneNumber3 || "";
    this.Appointment.TEL4 = this.myForm?.value?.phoneNumber4 || "";

    let objSave = {
      Identifier: "",
      AppointmentNo: null,
      IndividualId: this.Appointment?.IDENTIFIER?.toString() || '',
      ResourceId: this.appoitmentvalue?.resourceid?.toString() || null,
      AutoResource: this.Appointment?.AutoResource || null,
      AppointmentDate: this.appoitmentvalue?.requestedTime?.toString() || null,
      AppointmentDuration: this.appoitmentvalue?.defaultduration || null,
      NoShowIndicator: this.Appointment?.NoShowIndicator || null,
      NormalBookingStatus: this.Appointment?.NormalBookingStatus || null,
      BlockMoveStatus: this.Appointment?.BlockMoveStatus || null,
      IsConsultation: this.Appointment?.IsConsultation || null,
      FinancialClass: this.Appointment?.FinancialClass || null,
      CancelIndicator: this.Appointment?.CancelIndicator || null,
      AppointmentOverBooking: this.Appointment?.AppointmentOverBooking || null,
      NamedReferral: this.Appointment?.NamedReferral || null,
      SessionId: this.appoitmentvalue?.sessionid?.toString() || null,
      ReferralName: this.Appointment?.ReferralName || null,
      NoShowCancelDate: new Date().toISOString(),
      BlockMoveCount: this.Appointment?.BlockMoveCount || null,
      VisitTypeCode: this.Appointment?.VisitTypeCode || null,
      ReferralCode: this.Appointment?.ReferralCode || null,
      CaseSheetAvailable: this.Appointment?.CaseSheetAvailable || null,
      BlockmoveType: this.Appointment?.BlockmoveType || null,
      DoctorCode: this.appoitmentvalue?.resourceid?.toString() || null,
      RoomCode: this.Appointment?.RoomCode || null,
      EquipmentId: this.Appointment?.EquipmentId || null,
      Referral: this.Appointment?.Referral || null,
      VisItid: this.Appointment?.VisItid || null,
      SpecialityCode: this.appoitmentvalue?.speciality?.toString() || null,
      CancelledBy: this.Appointment?.CancelledBy || null,
      ResourceRemarks: this.Appointment?.ResourceRemarks || null,
      ApptRemarks: this.Appointment.ApptRemarks || null,
      ClinicCode: this.appoitmentvalue?.clinic?.toString() || null,
      RequestedTime: this.appoitmentvalue?.requestedTime?.toString() || null,
      TrialCase: this.Appointment.TrialCase || null,
      ForceInReason: this.Appointment.ForceInReason || null,
      ForceInUserid: this.Appointment.ForceInUserid || null,
      PrevApptNo: this.Appointment.PrevApptNo || null,
      FirstAvailableIndicator: this.Appointment.FirstAvailableIndicator || null,
      ProgrammeType: this.Appointment.ProgrammeType || null,
      LocationCode: this.Appointment.LocationCode || null,
      SeriesNo: this.Appointment.SeriesNo || null,
      SlotNo: this.appoitmentvalue?.selectedSlotInfo,
      ApptType: 1,
      IndividualModelData: {
        Identifier: this.Appointment?.IDENTIFIER?.toString() || '',
        IDNo: this.searchQuery?.toString() || '',
        IDType: this.searchtype || null,
        Name: this.Appointment?.NAME?.toString() || '',
        Merged: 'Y',
        PatientId: null,
        Gender: this.Appointment.GENDER || null,
        Title: this.Appointment.TITLE || null,
        Ethnicity: this.Appointment.ETHNICITY || null,
        DateOfBirth: this.Appointment.DOB || '',
        ResidenceCode: null,
        AddressType: this.Appointment.ADDRESSTYPE || null,
        Telephone: this.Appointment.TEL,
        Telephone2: this.Appointment.TEL2,
        Telephone3: this.Appointment.TEL3,
        Telephone4: this.Appointment.TEL4,
        Address1: this.Appointment?.ADDRESS1 || '',
        Address2: this.Appointment?.ADDRESS2 || '',
        Address3: this.Appointment?.ADDRESS3 || '',
        Address4: this.Appointment?.ADDRESS4 || '',
        PostalCode: this.Appointment?.POSTALCODE || '',
        State: this.Appointment?.STATE?.toString() || '',
        City: this.Appointment?.CITY?.toString() || '',
        Country: this.Appointment?.COUNTRY || 0,
        Email: this.Appointment?.EMAIL?.toString() || '',
        AlertEmail: this.alertEmail ? 'Y' : null,
        AlertSMS: this.alertSMS ? 'Y' : null,
        OrganDonor: this.organDonor ? 'Y' : null,
        BloodGroup: null,
        Nationality: this.Appointment?.NATIONALITY || null,
        PlaceOfBirth: this.Appointment?.PLACE_OF_BIRTH || null,
        VIPStatus: null,
        LanguageCode: this.Appointment?.LANGUAGECODE || null,
        TitleCode: 0,
        Remarks: '',
        Education: this.Appointment?.EDUCATION || null,
        Occupation: this.Appointment?.OCCUPATION || null,
        SpeakEnglish: this.speakEnglish ? 'Y' : null,
        MaritalStatus: this.Appointment?.MARITALSTATUS || null,
        Religion: this.Appointment?.RELIGION || null,
        DBOperationFlag: null,
        HRN: this.Appointment?.HRN || '',
        _OrgCode: 0
      },
        DOATransactionModel: this.doadata,
      TOATransactionModel: this.toadata
    }
    this.dialogsavelist = objSave
    console.log(objSave, 'objSave');
    this.saveEvent.emit(objSave);
    this.Resetvalue();
  }

  getTimeFromDate(datetime: string): string {
    const timeString = datetime.split('T')[1];
    return timeString;
  }
  
}