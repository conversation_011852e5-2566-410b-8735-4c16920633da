
    <his-findlistlegend></his-findlistlegend>
<div class="table-carousel-container ">
    <button [ngClass]="{'poiter-arrow': true, 'disabled': isArrowDisabled}" (mouseenter)="enableArrow()"
        (mouseleave)="disableArrow()" alt="arrow" class="arrow left-arrow" (click)="prev1()">&#10094;</button>
    <div class=" table-container overflow-x-hidden overflow-y-auto border-1" ecui-height-fill>
        <table class="rounded-2xl items-center w-screen">
            <thead class="">
                <tr>
                    <th class="resourceheader">
                        Resource Name</th>
                        @for(dayObj of headerdata; track $index){
                            <th >
                                {{ dayObj.dateheader }}<br>
                                <span [ngClass]="{'description-style': dayObj.description}">{{ dayObj.description }}</span>
                            </th>
                        }
                
                </tr>
            </thead>
            <tbody>
                @for(Resource of objsession; track $index ){
                    <tr>
                        <td class="resourcename">
                            {{ Resource.description }}
                        </td>
                        @for(dayObj of headerdata; track $index){
                            <td >
                                @if(Resource.dates.hasOwnProperty(dayObj.dateheader);){
                                    <div >
                                        @if(Resource.dates[dayObj.dateheader].length > 0){
                                            <div >
                                                <div *ngFor="let slot of sortSlotsBySession(Resource.dates[dayObj.dateheader])">
                                                    <div type="button"
                                                        [style.border-top]="'4px solid ' + (slot.preferredtimeavl > 0 ? ' #74c2db' : 'lightred')"
                                                        [ngClass]="getButtonClass(slot)" (click)="slotgeneratepopup(slot)"
                                                        matTooltip="{{' Start Time: ' + getFormattedTime(slot.startitme ) +',End Time: ' + getFormattedTime(slot.endtime ) + ', Slots Available: ' + slot.preferredtimeavl}}"
                                                        class="session-button">
                                                        {{ slot.sessiondesc | slice:0:3 }} - {{ slot.availablepercentage }}%
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                }
                            </td>
                        }
                    </tr>
                }
            </tbody>
        </table>
    </div>
    <button [ngClass]="{'poiter-arrow': true, 'disabled': isArrowDisabled}" (mouseenter)="enableArrow()"
        (mouseleave)="disableArrow()" alt="arrow" class="arrow right-arrow " (click)="next1()">&#10095;</button>
</div>