.legend {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.legend-color {
  width: 13px;
  height: 14px;
  border-radius: 21%;
  margin-right: 5px;
}

.legend-label {
  font-size: 11px;
  font-weight: bold;
  color: #333;
}

.custom-dropdown {
  position: relative;
  display: inline-block;
  font-size: larger;
  font-weight: 400;
}

.dropdown-selection mat-icon {
  margin-right: 8px;
}

.dropdown-selection input {
  border: none;
  outline: none;
  flex-grow: 1;
  font-size: smaller;
}

.dropdown-selection {
  padding: 14px 0px;
  border: 1px solid #ccc;
  cursor: pointer;
  height: 56px;
  overflow: hidden;
  border-radius: 5px;
  font-size: large;
  font-weight: 400;
  margin-left: -19px;
  padding-left: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.session-button1 {
  display: block;
  width: 98%;
  height: 30px;
  max-height: 100%;
  text-align: center;
}

.dropdown-menu {
  position: absolute;
  background-color: #f7fbfd;
  min-width: 214px;
  border: 1px solid #ccc;
  z-index: 11;
  display: flex;
  max-height: 194px;
  overflow-y: auto;
  margin-left: -17px;
}

.time-column {
  flex: 1;
  overflow-y: auto;
}

.time-column div {
  padding: 10px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
}

.time-column div:hover,
.time-column div.selected {
  background-color: #4d8ea9;
}

.selected {
  background-color: #007bff;
  color: white;
}

.dropdown-label {
  position: absolute;
  top: -10px;
  left: -10px;
  background-color: #fff;
  padding: 0px 5px;
  font-size: 13px;
  color: rgb(16 15 15 / 54%);
}

.time-range-picker {
  display: flex;
  align-items: center;
  width: 100%;
}

.inputbox.from-time,
.inputbox.to-time {
  margin: 0;
}

.schedule-table {
  width: 100%;
  margin-bottom: 5px;
  border-collapse: collapse;
  text-align: left;
  table-layout: fixed;
}

.schedule-table th,
.schedule-table td {
  border: 1px solid #ddd;
  height: 70px;
  vertical-align: baseline;
  text-align: center;
  font-size: 14px;
}

.schedule-table th {
  background-color: #f9f9f9;
}

.to-time-separator {
  margin: 0 8px;
  white-space: nowrap;
}

.available-slot {
  background-color: #89d4f6;
  color: white;
  padding: 14px;
}

.fixed {
  width: 1380px;
  overflow: hidden;
}

.date-navigation-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1px;
}

.date-nav-button {
  background-color: #ffffff;
  border: 2px solid #2e4f9c;
  border-radius: 20%;
  padding: 2px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.date-nav-button:hover {
  transform: scale(1.1);
}

.date-nav-button .material-icons {
  font-size: 24px;
}

.booked-slot {
  background-color: #f04e4e;
  color: white;
  padding: 14px;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-gap: 10px;
}

.grid-item button {
  padding: 7px;
  width: 100%;
  box-sizing: border-box;
}

.rounded-2xl {
  border-radius: 0.5rem;
}

.session-button {
  display: block;
  width: 100%;
  height: 40px;
  max-height: 100%;
}

.selected {
  background-color: #007bff;
  color: white;
}

.green-button {
  background-color: #c9ecee;
}

.orange-button {
  background-color: orange;
}

.red-button {
  background-color: rgb(246 94 94);
}

.lite-red-button {
  background-color: #f6a1aa;
}

.description-style {
  background-color: #009688;
  color: white;
  padding: 0.5em 1em;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 23px;
  font-size: small;
  text-align: center;
}

thead th {
  position: sticky;
  padding-top: 20px;
  background-color: #ededed;
  z-index: 2;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
  vertical-align: baseline;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin: auto;
}

thead th {
  position: sticky;
  top: 0;
  background-color: #ededed;
  z-index: 2;
  vertical-align: baseline;
}

tbody,
td,
tfoot,
th,
thead,
tr {
  border-color: inherit;
  border-width: 0.01px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 213px;
  text-align: center;
  flex: 1;
  text-align: center;
  flex: 1;
  text-align: center;
  vertical-align: initial;
}

.resourceheader {
  text-align: center;
  margin-top: 30px;
  min-width: 50px;
  font-weight: bold;
}

.resourcename {
  text-align: left;
  min-width: 50px;
  font-weight: bold;
}

.example-button-row button,
.example-button-row a {
  margin-right: 8px;
}

.right {
  justify-content: right;
  display: flex;
  padding-top: 25px;
}

.right1 {
  justify-content: right;
  display: flex;
  padding-top: 3px;
}

.time-picker {
  position: relative;
}

.time-picker::-webkit-calendar-picker-indicator {
  background-repeat: no-repeat;
  display: inline-block;
  fill: grey;
  height: 20px;
  width: 30px;
}

:host ::ng-deep .mat-form-field-infix {
  display: flex;
}

.example-disabled {
  display: inline-block;
}

.inputbox {
  width: 100%;
  font-weight: bold;
}

.inputbox1 {
  width: 100%;
}

.inputbox2 {
  width: 100%;
}

.matdate {
  height: 49px;
}

.matdate1 {
  height: 49px;
  width: 213px;
}

#datepicket {
  width: 212px;
}

th {
  white-space: nowrap;
  vertical-align: bottom;
  color: black;
}

td div {
  margin-bottom: 0px;
  padding-top: 1px;
  margin-left: 1px;
  margin-right: 1px;
  margin-block: auto;
}

.full-booking {
  background-color: red;
  color: white;
}

.half-booking {
  background-color: orange;
  color: white;
}

.not-available {
  color: rgb(103, 129, 245);
}

.sessions span {
  display: block;
  text-align: center;
  height: 33px;
}

.not-full-booking {
  background-color: green;
  color: white;
}

.holiday-no-booking {
  background-color: #f1f5f9;
  color: white;
}

.sunday {
  background-color: red;
  color: white;
}

.holiday-label {
  color: red;
  font-weight: bold;
  margin-left: 5px;
}

.colheader {
  text-align: center;
  color: #f1f5f9;
}

.medium-blue-button {
  background-color: #b9dbfe;
}

.time-slot-container button {
  background-color: #f2f2f2;
  color: #333;
  border: 1px solid #ccc;
  padding: 10px 20px;
  margin: 5px;
  cursor: pointer;
  font-size: 16px;
  border-radius: 5px;
  outline: none;
  transition: background-color 0.3s, transform 0.3s;
}

.time-slot-container button:hover {
  background-color: #e7e7e7;
  transform: translateY(-2px);
}

.time-slot-container button.selected {
  background-color: #4CAF50;
  color: white;
  border: 1px solid #367a38;
}

.mat-form-field {
  width: 100%;
}

.row-btn {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

th .mat-icon {
  color: white;
}

.table-wrapper {
  position: relative;
  overflow: hidden;
}

.table-container {
  // max-height: 515px;
  // overflow-y: auto;
  // overflow-x: hidden;
  position: relative;
  display: flex;
}

.arrow {
  cursor: pointer;
  user-select: none;
  padding: 10px;
  font-size: 44px;
  z-index: 10;
  color: #2e4f9c;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  width: 50px;
}

.table-carousel-container {
  position: relative;
  align-items: center;
  display: flex;
  justify-content: center;
  max-width: 100%;
  overflow-x: auto;
}

.carousel-control {
  position: absolute;
  top: 50%;
  background-color: #fff;
  border: none;
  cursor: pointer;
  padding: 10px;
  z-index: 3;
  transform: translateY(-50%);
}

.left-arrow {
  left: 0;
}

.right-arrow {
  right: 0;
}

.poiter-arrow {
  cursor: default;
  color: grey;
  ;
}

.poiter-arrow:hover {
  cursor: pointer;
  color: #2e4f9c;
  ;
}

.calendar {
  display: flex;
  flex-direction: column;
  font-family: 'Roboto', sans-serif;
  color: #333;
  width: 1100px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.calendar-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 45%;
  background-color: #f1f3f4;
}

.calendar-header {
  display: flex;
  background-color: #f1f3f4;
}

.header-day {
  flex: 1;
  text-align: center;
  font-weight: bold;
  padding: 10px 0;
  border-right: 1px solid #e0e0e0;
}

.header-day:last-child {
  border-right: none;
}

.calendar-body {
  display: flex;
  flex-direction: column;
}

.week {
  display: flex;
}

.day {
  flex-grow: 1;
  flex-basis: calc(100% / 7);
  border: 1px solid #ddd;
  min-height: 77px;
  position: relative;
  background-color: #fff;
}

.date-header {
  position: absolute;
  top: 8px;
  left: 8px;
  font-weight: bold;
  color: #5f6368;
}

.resource-availability {
  padding-top: 30px;
}

.not-in-month {
  background-color: #ffffff;
}

.empty-day {
  background-color: transparent;
}

.mdc-snackbar__surface {
  background-color: white !important;
  border: none !important;
  border-left: solid 3px #DD2F2F !important;

}

.mat-mdc-snack-bar-label {
  color: #DD2F2F !important;
  font-weight: 600 !important;
}