import { CommonModule, DatePipe } from '@angular/common';
import { Component, Input, Output, EventEmitter, OnInit, SimpleChanges } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import {MatTooltipModule} from '@angular/material/tooltip';
import { Router } from '@angular/router';
import { MaterialPackage} from '@app-appt/utils';
import { FillHeightDirective } from 'ec-ngcore/ui';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ApplicationService } from 'ec-ngcore';
import { DateTimeService } from '@app-appt/services';
import { FindlistlegendComponent } from '../findlistlegend/findlistlegend.component';
import { SlotSelectorComponent } from '../slot-selector/slot-selector/slot-selector.component';
@Component({
  selector: 'his-appointment-scheduler',
  standalone: true,
  imports: [CommonModule, FillHeightDirective,  MaterialPackage, FindlistlegendComponent],
  templateUrl: './appointment-scheduler.component.html',
  styleUrl: './appointment-scheduler.component.scss',
  providers: [DatePipe, DateTimeService]
})

export class AppointmentSchedulerComponent implements OnInit {
  @Input() headerdata: any[]=[];
  @Input() objsession: any[]=[];
  @Input() objectsearch?: any;
  @Input() dateStatus?: any;
  @Output() arrival = new EventEmitter();
  //currentMonth: moment.Moment;
  currentDate = new Date();
  selectedStartDate: Date;
  selectedEndDate: Date;
  minAllowedDate: Date;
  maxAllowedDate: Date;
  isArrowDisabled: boolean = true;

  ngOnChanges(changes: SimpleChanges) {
    const objsearch = changes["objectsearch"];
    if (objsearch && !objsearch.firstChange) {
      this.updateDateRange(objsearch.currentValue.fromDate, 
                objsearch.currentValue.toDate);
    };
    if (changes['headerdata']) {
      if (this.headerdata && this.headerdata.length > 0) {
      }
      if (changes['dateStatus']) {
        if (this.dateStatus && this.dateStatus.length > 0) {
        }
      }
    }
  }

  public sortSlotsBySession(slots: any[]): any[] {
    if (!slots) return [];

    return [...slots].sort((a, b) => {
      if (a.sessiondesc === 'AM SESSION' && b.sessiondesc === 'PM SESSION') {
        return -1; // AM comes first
      }
      if (a.sessiondesc === 'PM SESSION' && b.sessiondesc === 'AM SESSION') {
        return 1; // PM comes after
      }
      return 0; // maintain order for same session type
    });
  }
  constructor(
    public router: Router,
    public datePipe: DatePipe,
    public dialog: MatDialog,
    public snackbar: MatSnackBar,
    private _appService: ApplicationService, private dateTimeService: DateTimeService

  ) {
    this.selectedStartDate = new Date();
    this.selectedEndDate = new Date();
    this.selectedEndDate.setMonth(this.selectedEndDate.getMonth()+1);
    this.generateTimes();
    //this.currentMonth = moment();
    this.minAllowedDate = new Date();
    this.maxAllowedDate = new Date();
    this.displayFromDate = new Date(this.objLoad.fromDate.getTime());
    this.displayToDate = new Date(this.objLoad.toDate.getTime());;
  }

  ngOnInit(): void {
    this.objLoad.fromDate = this.objectsearch.fromDate;
    this.objLoad.toDate = this.objectsearch.toDate;
    if (this.objectsearch) {
      this.updateDateRange(this.objectsearch.fromDate, this.objectsearch.toDate);
    }
  }

  private updateDateRange(fromDate: Date, toDate: Date): void {
    this.objLoad.fromDate = fromDate;
    this.objLoad.toDate = toDate;
    this.displayFromDate = new Date(fromDate.getTime());
    this.displayToDate = new Date(toDate.getTime());
    this.initializeDates();
  }

  visibleRange: number = 7;
  today = new Date();
  times: string[] = [];
  isOpen: boolean = false;
  fromTime: string = '';
  toTime: string = '';
  displayValue: string = '';
  objLoad: any = {
    id: '',
    resourcetype: 0,
    fromDate: new Date(),
    toDate: new Date(),
    clinic: '',
    fromtime: '',
    totime: '',
    speciality: '',
    sessionDate: '',
    sessionId: '',
    sessionFromDate: '',
    sessionToDate: '',
    resource: 0,
    appointmentId: '',
  };
  searchTerm: string = '';
  slotbooking: any;

  getFormattedTime(slotTime: string): string {
    return this.dateTimeService.getFormattedTime(slotTime);
  }

  getUniqueHeaders(bookinglist: any[]): string[] {
    const uniqueHeadersSet = new Set<string>();
    for (const header of bookinglist) {
      uniqueHeadersSet.add(header.SESSIONDESC);
    }
    return Array.from(uniqueHeadersSet);
  }

  generateTimes(): void {
    for (let i = 0; i < 24; i++) {
      this.times.push(`${i.toString().padStart(2, '0')}:00`);
      this.times.push(`${i.toString().padStart(2, '0')}:30`);
    }
  }

  toggleDropdown(event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }
    this.isOpen = !this.isOpen;
  }

  selectTime(time: string, isFromTime: boolean): void {
    if (isFromTime) {
      this.fromTime = time;
    } else {
      this.toTime = time;
    }
    this.updateDisplayValue();
  }

  closeDropdown(): void {
    setTimeout(() => {
      this.isOpen = false;
    }, 100);
  }

  updateDisplayValue(): void {
    this.displayValue = `${this.fromTime || 'HH:mm'} - ${this.toTime || 'HH:mm'}`;
    this.objLoad.fromTime = this.fromTime;
    this.objLoad.toTime = this.toTime;
    this.isOpen = false;
  }

  onInput(value: string): void {
    let numericValue = value.replace(/[^\d]/g, '');
    numericValue = numericValue.slice(0, 11);
    const insertColon = (val:string) => val.length > 2 ? `${val.slice(0, 2)}:${val.slice(2)}` : val;
    let start = numericValue.slice(0, 4);
    let end = numericValue.slice(4);
    start = insertColon(start);
    end = insertColon(end);
    this.objLoad.fromTime = start;
    this.objLoad.toTime = end;
    this.displayValue = start;
    if (end) {
      this.displayValue += ' - ' + end;
    }
  }
  setDefaultDateRange() {
    const today = new Date();
    const endDay = new Date();
    endDay.setDate(today.getDate() + 6);
    this.objLoad.fromDate = today;
    this.objLoad.toDate = endDay;
  }
  getTimeInAMPMFormat(timeString: string): string {
    const timeParts = timeString.split(':');
    if (timeParts.length === 3) {
      const hours = parseInt(timeParts[0], 10);
      const minutes = parseInt(timeParts[1], 10);
      const seconds = parseInt(timeParts[2].split('+')[0], 10);
      if (!isNaN(hours) && !isNaN(minutes) && !isNaN(seconds)) {
        const date = new Date(2000, 0, 1, hours, minutes, seconds);
        return this.datePipe.transform(date, 'hh:mm a') ||'';
      }
    }
    return 'Invalid Time';
  }
  initializeDates(): void {
    this.headerdata = [];
    for (let i = 0; i < this.visibleRange; i++) {
      const date = new Date(this.displayFromDate);
      date.setDate(date.getDate() + i);
      const descData = this.dateStatus.filter((x:any) =>
        this.datePipe.transform(date, 'yyyy-MM-dd') === this.datePipe.transform(x.ORGDATE, 'yyyy-MM-dd')
      );
      this.headerdata.push({
        dateheader: date.toISOString().split('T')[0],
        description: descData.length > 0 ? descData[0].DESCRIPTION : ''
      });
    }
  }
  datevalue() {
    this.objLoad.fromDate = this.objectsearch.fromDate;
    this.objLoad.toDate = this.objectsearch.toDate;
  }
  displayFromDate: Date = new Date(this.objLoad.fromDate.getTime());
  displayToDate: Date = new Date(this.objLoad.toDate.getTime());
  next1() {
    const newDisplayFromDate = new Date(this.displayFromDate);
    newDisplayFromDate.setDate(newDisplayFromDate.getDate() + this.visibleRange);
    if (newDisplayFromDate <= this.objLoad.toDate) {
      this.displayFromDate = newDisplayFromDate;
      this.initializeDates();
    } else {
      this._appService.alertDialog({
        'title': 'System Error',
        message: 'Cannot navigate after to your end date.'
      });
    }
  }

  prev1() {
    const newDisplayFromDate = new Date(this.displayFromDate);
    newDisplayFromDate.setDate(newDisplayFromDate.getDate() - this.visibleRange);
    if (newDisplayFromDate >= this.objLoad.fromDate) {
      this.displayFromDate = newDisplayFromDate;
      this.initializeDates();
    } else {
      this._appService.alertDialog({
        'title': 'System Error',
        message: 'Cannot navigate before to your start date.'
      });
    }
  }

  onDateRangeSelected(newFromDate: Date, newToDate: Date) {
    this.objLoad.fromDate = newFromDate;
    this.objLoad.toDate = newToDate;
    this.displayFromDate = new Date(newFromDate.getTime());
    this.displayToDate = new Date(newToDate.getTime());
    this.initializeDates();
    this.updateDateRange(newFromDate, newToDate);
  }
  displayedColumns = ['resourceName']
  enableArrow() {
    this.isArrowDisabled = false;
  }

  disableArrow() {
    this.isArrowDisabled = true;
  }

  getButtonClass(resourceDate:{availablepercentage:number,
                               preferredtimeavl:number,}) {
    let classes = {
      'red-button': resourceDate.availablepercentage <= 10, // Highest priority condition
      'medium-blue-button': this.objLoad.fromTime && this.objLoad.toTime && resourceDate.preferredtimeavl > 0 && resourceDate.availablepercentage > 10, // Second highest priority, changed from orange to medium blue
      'green-button': resourceDate.availablepercentage >= 10 && resourceDate.availablepercentage <= 100 && !(this.objLoad.fromTime && this.objLoad.toTime && resourceDate.preferredtimeavl > 0), // Third priority, ensuring red and blue conditions are not met
      'lite-red-button': resourceDate.preferredtimeavl === 0 && resourceDate.availablepercentage > 10, // Adjusted to respect the highest priority
    };
    return classes;
  }

  public slotgenerate(slot: any): void {
    this.slotbooking = slot;
    this.slotbooking.resourceid = slot.resourceid
  }

  slotgeneratepopup(slot: any): void {
    this.slotbooking = slot;
    const dialogData = {
      ...slot,
      clinic: this.objectsearch.clinic,
      speciality: this.objectsearch.speciality,
      resourcetype: this.objectsearch.resourcetype,
      fromtime: this.objectsearch.fromtime,
      totime: this.objectsearch.totime
    };
    const dialogRef = this.dialog.open(SlotSelectorComponent, {
      disableClose: true,
      autoFocus: true,
      width: "90vw",
      // height: "90vh",
      data: dialogData
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.arrival.emit("Arrival");
      }
    });
  }
}

