<div class="p-4 grid grid-cols-1 lg:grid-cols-3 gap-6 border rounded-lg">

  <!-- Left Column - Patient Details -->
  <div class="lg:col-span-1 space-y-6">
    <!-- Patient Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
      <div class="p-6">
        <div class="flex justify-between items-start">
          <h2 class="text-xl font-bold text-blue-800">{{ viewdetails?.NAME }}</h2>
          <mat-icon class="text-blue-600 cursor-pointer hover:text-blue-800 print:hidden" 
                   svgIcon="mat_solid:edit" (click)="openDialogindividual()"></mat-icon>
        </div>
        
        <div class="flex flex-wrap gap-1">
          <span class="text-base font-medium px-1 rounded-full  text-grey-800" 
                [matTooltip]="viewdetails?.DOB | date:'dd-MM-yyyy'">{{ calculateAge(viewdetails?.DOB) }} Years</span>
          <span class="text-base font-medium px-1 rounded-full  text-grey-800">{{ viewdetails?.GENDER_DESC | titlecase }}</span>
          <span class="text-base font-medium px-1 rounded-full  text-grey-800">{{ viewdetails?.COUNTRY_DESC | titlecase }}</span>
        </div>
        
        <div class="mt-2">
          <span class="text-base text-gray-500 font-medium">ID No</span>
          <p class="text-base font-medium text-gray-700" [matTooltip]="viewdetails?.IDTYPE_DESC | titlecase">{{ viewdetails?.IDNO }}</p>
        </div>
      </div>
    </div>

    <!-- Contact Card -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-blue-800 mb-4">Contact Details</h3>
        
        <div class="space-y-1">
          <div class="flex items-center gap-3">
            <mat-icon class="text-blue-500" svgIcon="mat_solid:phone"></mat-icon>
            <div>
              <p class="text-base text-gray-700">{{ viewdetails?.TEL }}</p>
              <p class="text-base text-gray-700" *ngIf="viewdetails?.TEL2">{{ viewdetails?.TEL2 }}</p>
            </div>
          </div>
          
          <div class="flex items-center gap-3">
            <mat-icon class="text-blue-500" svgIcon="mat_solid:mail"></mat-icon>
            <p class="text-base text-gray-700">{{ viewdetails?.EMAIL }}</p>
          </div>
          
          <div class="flex items-start gap-3">
            <mat-icon class="text-blue-500" svgIcon="mat_solid:location_on"></mat-icon>
            <p class="text-base text-gray-700">
              {{ viewdetails?.ADDRESS1 }}<br>
              {{ viewdetails?.ADDRESS2 }}<br>
              {{ viewdetails?.ADDRESS3 }}<br>
              {{ viewdetails?.ADDRESS4 }}<br>
              {{ viewdetails?.POSTALCODE }}<br>
              {{ viewdetails?.COUNTRY_DESC }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Center Column - Appointment Details -->
  <div class="lg:col-span-1">
    <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 h-full">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Appointment Details</h3>
        
        <div class="space-y-1">
          <div class="flex justify-between items-center font-semibold">
            <span class="text-base text-blue-900 font-bold">Appointment No</span>
            <div class="flex items-center gap-2">
              <p class="text-base font-medium text-blue-900 font-bold">{{ viewdetails?.APPOINTMENTNO }}</p>
              <mat-icon class="text-blue-600 cursor-pointer hover:text-blue-800 print:hidden" 
                       svgIcon="mat_solid:edit_calendar" (click)="editappointment()"></mat-icon>
            </div>
          </div>
          
          <div class="flex justify-between items-center font-semibold">
            <span class="text-base text-blue-900 font-bold">Date & Time</span>
            <p class="text-base font-medium text-gray-900">
              {{ viewdetails?.APPOINTMENTDATE | date:"dd/MM/yyyy" }} - {{ viewdetails?.REQUESTEDTIME | date: 'HH:mm' }}
            </p>
          </div>
          
          <div class="flex justify-between items-center font-semibold">
            <span class="text-base text-blue-900 font-bold">Resource</span>
            <p class="text-base font-medium text-gray-900">{{ viewdetails?.RESOURCE_DESC }}</p>
          </div>
          
          <div class="flex justify-between items-center font-semibold" *ngIf="viewdetails?.CLINIC_DESC">
            <span class="text-base text-blue-900 font-bold">Clinic</span>
            <p class="text-base font-medium text-gray-900">{{ viewdetails?.CLINIC_DESC }}</p>
          </div>
          
          <!-- QR Code at bottom of center column -->
          <div class="mt-8 pt-4 border-t border-gray-200 flex flex-col items-center">
            <div class="bg-white p-2 rounded-lg shadow-sm">
              <qrcode [qrdata]="getQRData()" [width]="120"></qrcode>
            </div>
            <p class="text-xs text-gray-500 mt-2">Scan for appointment details</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Right Column - Do On Arrival -->
  <div class="lg:col-span-1">
    <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 h-full">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Do On Arrival</h3>
        
        <div class="space-y-3">
          @for(item of doonarrivalview; track $index){
            <div class="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
              <span class="text-sm font-medium text-blue-700 min-w-[30px]">{{ item.CODE }}</span>
              <span class="text-sm text-gray-700">{{ item.DESCRIPTION }}</span>
            </div>
          }
        </div>
        
        <div class="mt-6 pt-4 border-t border-gray-200" *ngIf="viewdetails?.DISCLAIMER">
          <p class="text-xs text-gray-500 italic">{{ viewdetails?.DISCLAIMER }}</p>
        </div>
      </div>
    </div>
  </div>
</div>