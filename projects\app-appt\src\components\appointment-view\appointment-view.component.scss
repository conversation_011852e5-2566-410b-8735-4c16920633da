/* Custom styles for elements not covered by Tailwind */
.hide-in-print {
  @apply block;
}

/* Print-specific styles */
@media print {
  .hide-in-print {
    display: none !important;
  }
  
  body {
    overflow: visible;
    font-size: 12px;
  }
  
  .shadow-lg {
    box-shadow: none !important;
    border: 1px solid #ddd;
  }
}

/* Ensure mat-icons have proper sizing */
mat-icon {
  font-size: inherit !important;
  width: 1em !important;
  height: 1em !important;
}

/* QR Code container styling */
qrcode {
  display: block;
}

/* Mat-divider styling */
mat-divider {
  @apply border-gray-200;
}