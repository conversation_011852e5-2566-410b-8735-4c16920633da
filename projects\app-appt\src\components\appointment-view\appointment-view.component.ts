import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import { QRCodeComponent } from 'angularx-qrcode';
import { MatDialog } from '@angular/material/dialog';
import { AppointmentsAPIService } from 'ecmed-api/appointments';
import { PatientIndividualFormComponent,CommonService } from 'his-components';
import { MaterialPackage} from '@app-appt/utils';
@Component({
  selector: 'his-appoitmentview',
  standalone: true,
  imports: [CommonModule,  QRCodeComponent, MaterialPackage],
  templateUrl: './appointment-view.component.html',
  styleUrl: './appointment-view.component.scss'
})
export class AppoitmentviewComponent implements OnInit {
  @Input() doonarrivalview: any;
  @Input() traceonarrivalview: any;
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['traceonarrivalview']) {
      const currentValue = changes['traceonarrivalview'].currentValue;
      this.doSomethingWithNewValue(currentValue);
    }
    if (changes['doonarrivalview']) {
      const currentValue = changes['doonarrivalview'].currentValue;
      this.doSomethingWithNewValue(currentValue);
    }
  }

  private doSomethingWithNewValue(value: any): void {
  }
  @Output() editindividual: EventEmitter<void> = new EventEmitter<void>();

  @Output() edit: EventEmitter<void> = new EventEmitter<void>();
  @Input() viewdetails: any

  appointmentId: string | null = null;
  appointmentview: any;
  Appointmentselected: any;
  id: string | null = null;

  constructor( public _HISAPI: AppointmentsAPIService, public commServ: CommonService,
    public dialog: MatDialog) { }

  ngOnInit() {
  }

  getQRData(): string {
    const appointment = this.viewdetails?.QRTAG;
    return `Appoinment :${appointment}`;
  }

  editappointment() {
    this.edit.emit()

  }
  openDialogindividual() {
    const dialogData = {
      id: this.viewdetails.IDENTIFIER1
    };
    const dialogRef = this.dialog.open(PatientIndividualFormComponent, {
      width: "2500px",
      height: "850px",
      panelClass: "custom-dialog-container",
      data: dialogData
    });
    dialogRef.afterClosed().subscribe(result => {
    });
  }
  openDialog() {
    this.editindividual.emit(this.viewdetails.INDIVIDUAL);
  }

  calculateAge(dob:string): number {
    return this.commServ.calculateAge(dob);
  }
}
