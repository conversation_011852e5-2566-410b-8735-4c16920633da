import { Directive, ElementRef, HostListener } from '@angular/core';

@Directive({
  selector: '[ec-AutoFormatDate]',
  standalone: true
})
export class AutoFormatDateDirective {
  private readonly maxDay = 12;
  private readonly maxMonth = 31;

  constructor(private el: ElementRef<HTMLInputElement>) {}

  @HostListener('input', ['$event']) onInputChange(event: Event) {
    let input = (event.target as HTMLInputElement).value.replace(/[^0-9]/g, ''); // Remove non-numeric characters

    if (input.length > 2) {
      let day = parseInt(input.substring(0, 2));
      day = Math.min(day, this.maxDay);  // Ensure day is not greater than 31
      input = `${day < 10 ? '0' + day : day}` + input.substring(2);
    }

    if (input.length > 4) {
      let month = parseInt(input.substring(2, 4));
      month = Math.min(month, this.maxMonth);  // Ensure month is not greater than 12
      input = input.substring(0, 2) + `${month < 10 ? '0' + month : month}` + input.substring(4);
    }

    // Insert slashes
    if (input.length > 4) {
      input = input.substring(0, 2) + '/' + input.substring(2, 4) + '/' + input.substring(4);
    } else if (input.length > 2) {
      input = input.substring(0, 2) + '/' + input.substring(2);
    }

    this.el.nativeElement.value = input;  // Set the modified value back to the input
  }
}
