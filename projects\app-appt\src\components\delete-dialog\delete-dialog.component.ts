import { Component } from '@angular/core';
import { Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogContent, MatDialogActions, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import  {MaterialPackage} from '@app-appt/utils';

@Component({
  selector: 'app-delete-dialog',
  standalone: true,
  imports: [MatDialogContent, MatDialogActions, MatFormFieldModule, MaterialPackage
  ],
  templateUrl: './delete-dialog.component.html',
  styleUrl: './delete-dialog.component.scss'
})
export class DeleteDialogComponent {
  constructor(public dialog: MatDialog, private ref: MatDialogRef<DeleteDialogComponent>, @Inject(MAT_DIALOG_DATA) public data: any
  ) { }

  public dialogClose(data: number) {
    this.dialog.closeAll();
    this.ref.close(data);
  }


}
