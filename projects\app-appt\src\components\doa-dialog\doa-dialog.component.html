<div class="bg-white rounded-lg shadow-lg h-full overflow-hidden">
    <!-- Dialog Header -->
    <div mat-dialog-title class="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
        <div class="text-lg font-medium text-gray-800">Do On Arrival Procedure</div>
        <button type="button" mat-icon-button mat-dialog-close
            class="text-gray-400 hover:text-gray-600 transition-colors" aria-label="Close Dialog">
            <mat-icon svgIcon="close"></mat-icon>
        </button>
    </div>

    <!-- Form Controls -->
    <div class="p-4 border-b border-gray-100 bg-white">
        <div class="flex gap-4 items-end">
            <mat-form-field appearance="outline" subscriptSizing="dynamic" class="flex-1">
                <mat-label>Service Template</mat-label>
                <mat-select [(ngModel)]="selectedProcedure" name="procedure">
                    <mat-option value="">--None--</mat-option>
                </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline" subscriptSizing="dynamic" class="flex-1">
                <mat-label>Procedures Types</mat-label>
                <mat-select [(ngModel)]="selectedProcedure" name="procedure">
                    @for(procedure of PROCEDURETYPES; track $index){
                    <mat-option [value]="procedure.IDENTIFIER">
                        {{ procedure.DESCRIPTION }}
                    </mat-option>
                    }
                </mat-select>
            </mat-form-field>
            <!-- Search Filter -->
            <mat-form-field appearance="outline" subscriptSizing="dynamic" >
                <mat-label>Search procedures...</mat-label>
                <input matInput [(ngModel)]="searchFilter" (ngModelChange)="onFilterChange()"
                    placeholder="Type to filter procedures">
                <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>

            <button matTooltip="Search" (click)="onSearch()" mat-raised-button
                class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md transition-colors shadow-sm">
                Search
            </button>

            <button matTooltip="Add Selected Items" (click)="sendSelectedItems()" mat-raised-button
                class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md transition-colors shadow-sm">
                Add
            </button>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="p-4 bg-gray-50  overflow-auto" ecui-height-fill>
        <div class="flex gap-4 h-full">

            <!-- Available Procedures Card -->
            <div class="flex-1 bg-white rounded-lg border border-gray-200 shadow-sm flex flex-col">
                <div class="p-3 border-b border-gray-200 bg-gray-50">
                    <div class="flex items-center justify-between mb-3">
                        <h6 class="text-sm font-medium text-gray-700 uppercase tracking-wide">Available Procedures</h6>
                        <span class="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded-full">
                            {{ filteredItems.length || 0 }} of {{ availableItems.length || 0 }}
                        </span>
                    </div>

              


                </div>
                <div class="flex-1 overflow-y-auto" style="max-height: calc(100vh - 300px);">
                    @if(filteredItems && filteredItems.length > 0) {
                    @for(item of filteredItems; track $index){
                    <div (click)="selectItem(item)"
                        class="flex items-center justify-between p-3 border-b border-gray-100 hover:bg-blue-50 cursor-pointer transition-colors group">
                        <span class="text-sm text-gray-700 font-medium">{{ item.CODE }} - {{ item.DESCRIPTION }}</span>
                        <mat-icon
                            class="text-gray-400 group-hover:text-blue-600 transition-colors text-lg">add_circle_outline</mat-icon>
                    </div>
                    }
                    } @else {
                    <div class="flex flex-col items-center justify-center h-full text-gray-500">
                        <mat-icon class="text-gray-300 text-4xl mb-2">inbox</mat-icon>
                        <p class="text-sm">No procedures available</p>
                    </div>
                    }
                </div>
            </div>

            <!-- Selected Procedures Card -->
            <div class="flex-1 bg-white rounded-lg border border-gray-200 shadow-sm flex flex-col">
                <div class="p-3 border-b border-gray-200 bg-gray-50">
                    <h6 class="text-sm font-medium text-gray-700 uppercase tracking-wide">
                        Selected Procedures
                        @if(selectedItems && selectedItems.length > 0) {
                        <span class="ml-2 bg-gray-200 text-gray-700 px-2 py-1 rounded-full text-xs">{{
                            selectedItems.length }}</span>
                        }
                    </h6>
                </div>
                <div class="flex-1 overflow-y-auto" style="max-height: calc(100vh - 300px);">
                    @if(selectedItems && selectedItems.length > 0) {
                    @for(selectedItem of selectedItems; track $index){
                    <div
                        class="flex items-center justify-between p-3 border-b border-gray-100 bg-blue-50 hover:bg-blue-100 transition-colors group">
                        <span class="text-sm text-gray-800 font-medium">
                            {{ selectedItem | getAttribute:'CODE' }} -
                            {{ selectedItem | getAttribute:'DESCRIPTION' }}
                        </span>
                        <button mat-icon-button (click)="removeSelectedItem(selectedItem)"
                            class="text-gray-400 hover:text-red-600 transition-colors" aria-label="Remove item">
                            <mat-icon class="text-lg">close</mat-icon>
                        </button>
                    </div>
                    }
                    } @else {
                    <div class="flex flex-col items-center justify-center h-full text-gray-500">
                        <mat-icon class="text-gray-300 text-4xl mb-2">playlist_add_check</mat-icon>
                        <p class="text-sm">No procedures selected</p>
                    </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>