// Main container styling
.overflow-x-hidden {
  background: #f8fafc;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  max-width: 900px;
  margin: 0 auto;
}

// Dialog title styling
.mat-dialog-title {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 16px 24px;
  border-radius: 8px 8px 0 0;
  
  .text-lg {
    font-weight: 600;
    color: #1e40af;
    font-size: 18px;
  }
}

// Form controls section
.form-controls {
  padding: 20px 24px;
  background: #ffffff;
  border-bottom: 1px solid #f1f5f9;
  
  .mat-form-field {
    margin-right: 16px;
    
    &:last-of-type {
      margin-right: 0;
    }
  }
  
  button {
    height: 40px;
    border-radius: 6px;
    font-weight: 500;
    text-transform: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
    
    &.mat-primary {
      background: #3b82f6;
      
      &:hover {
        background: #2563eb;
      }
    }
  }
}

// Main content area
.main-content {
  padding: 24px;
  background: #f8fafc;
  min-height: 400px;
  
  .cards-container {
    display: flex;
    gap: 24px;
    height: 100%;
  }
}

// Card styling
.card {
  flex: 1;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: box-shadow 0.2s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
  
  .header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
    padding: 16px 20px;
    
    h6 {
      margin: 0;
      color: #1e40af;
      font-weight: 600;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
  
  .content {
    height: 320px;
    overflow-y: auto;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f5f9;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #cbd5e1;
      border-radius: 3px;
      
      &:hover {
        background: #94a3b8;
      }
    }
  }
}

// Item styling
.item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  margin: 0;
  border-bottom: 1px solid #f1f5f9;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  
  &:hover {
    background: #f0f9ff;
    border-left: 3px solid #3b82f6;
    padding-left: 17px;
  }
  
  &:last-child {
    border-bottom: none;
  }
  
  span {
    color: #374151;
    line-height: 1.4;
  }
  
  &.selected {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: #ffffff;
    border-left: 3px solid #1d4ed8;
    padding-left: 17px;
    
    &:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    }
    
    span {
      color: #ffffff;
    }
    
    button {
      .mat-icon {
        color: #ffffff;
        font-size: 16px;
      }
    }
  }
}

// Scrollable areas
.scrollable,
.scrollable1 {
  padding: 0;
  background: #ffffff;
  
  .item:first-child {
    border-top: none;
  }
}

// Special styling for selected procedures card
.trace-on-arrival {
  .header {
    background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
    
    h6 {
      color: #92400e;
    }
  }
  
  .item.selected {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border-left-color: #b45309;
    
    &:hover {
      background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    }
  }
}

// Empty state
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
  font-style: italic;
}

// Responsive design
@media (max-width: 768px) {
  .cards-container {
    flex-direction: column;
    gap: 16px;
  }
  
  .card {
    .content {
      height: 200px;
    }
  }
  
  .form-controls {
    .mat-form-field {
      margin-right: 0;
      margin-bottom: 16px;
      width: 100%;
    }
    
    button {
      width: 100%;
      margin-bottom: 8px;
    }
  }
}

// Animation for smooth transitions
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.item {
  animation: fadeIn 0.3s ease;
}

// Focus states for accessibility
.item:focus,
button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

// Loading state
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: #6b7280;
}