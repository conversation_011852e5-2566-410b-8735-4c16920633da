import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { AppointmentsAPIService } from 'ecmed-api/appointments'
import { FillHeightDirective } from 'ec-ngcore/ui';
import { GetAttributePipe } from 'ec-ngcore';
import { MaterialPackage } from '@app-appt/utils';
import { DoaService } from '@app-appt/services/doa.service';

@Component({
  selector: 'his-arrivaldialog',
  standalone: true,
  imports: [FormsModule, CommonModule, MaterialPackage, GetAttributePipe, FillHeightDirective,],
  templateUrl: './doa-dialog.component.html',
  styleUrl: './doa-dialog.component.scss',
})

export class DOADialogComponent {
  availableItems: any[] = [];
  filteredItems: any[] = [];
  selectedItems: string[] = [];
  PROCEDURETYPES: any;
  selectedProcedure: number | null = null;
  searchFilter: string = '';
  selectedTypeFilter: string = '';
  constructor(
    public dialogRef: MatDialogRef<DOADialogComponent>,
    public _HISAPI: AppointmentsAPIService, public _arrivalservice: DoaService
  ) { }

  ngOnInit(): void {
    this.fetchProcedureTypes();
    this.fetchArrivalData();
  }
  fetchProcedureTypes(): void {
    this._HISAPI.appointmentsAPIInitAllGet().subscribe(
      (response: any) => {
        this.PROCEDURETYPES = response?.Codes?.PROCEDURETYPES || [];
      }
    );
  }
  fetchArrivalData(): void {
    this._HISAPI.appointmentsAPIGetDoOnArrivalGet().subscribe(
      (result: any) => {
        this.availableItems = result || [];
        this.filteredItems = [...this.availableItems];
      }
    );
  }

  onSearch(): void {
    if (this.selectedProcedure !== null) {
      this._HISAPI.appointmentsAPIGetDoOnArrivalGet(
        { id: this.selectedProcedure.toString() }).subscribe(
          (result: any) => {
            this.availableItems = result || [];
            this.applyFilter();
          }
        )
    }
  }

  selectItem(item: any): void {
    this.availableItems = this.availableItems.filter((i: any) => i !== item);
    this.selectedItems.push(item);
    this.applyFilter();
  }

  removeSelectedItem(item: any): void {
    const index = this.selectedItems.indexOf(item);
    if (index !== -1) {
      this.selectedItems.splice(index, 1);
      this.availableItems.push(item);
      this.applyFilter();
    }
  }

  onFilterChange(): void {
    this.applyFilter();
  }

  onTypeFilterChange(): void {
    this.applyFilter();
  }

  clearFilters(): void {
    this.searchFilter = '';
    this.selectedTypeFilter = '';
    this.applyFilter();
  }

  private applyFilter(): void {
    let filtered = [...this.availableItems];

    // Apply type filter first
    if (this.selectedTypeFilter && this.selectedTypeFilter.trim() !== '') {
      filtered = filtered.filter((item: any) => {
        return item.TYPE === this.selectedTypeFilter ||
          item.PROCEDURE_TYPE === this.selectedTypeFilter ||
          item.PROCEDURETYPE === this.selectedTypeFilter ||
          item.IDENTIFIER === this.selectedTypeFilter;
      });
    }

    // Apply search filter
    if (this.searchFilter && this.searchFilter.trim() !== '') {
      const filterText = this.searchFilter.toLowerCase().trim();
      filtered = filtered.filter((item: any) => {
        const code = item.CODE ? item.CODE.toLowerCase() : '';
        const description = item.DESCRIPTION ? item.DESCRIPTION.toLowerCase() : '';
        return code.includes(filterText) || description.includes(filterText);
      });
    }

    this.filteredItems = filtered;
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  sendSelectedItems(): void {
    this.dialogRef.close(this.selectedItems);
    console.log(this.selectedItems, "selectedItems");
    // this._arrivalservice.setarrival({
    //   selectarrival: this.selectedItems
    // });
  }
}
