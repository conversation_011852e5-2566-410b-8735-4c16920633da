<div class="flex  bg-gray-50 gap-4 overflow-hidden" ecui-height-fill>
  <!-- Do on Arrival Section -->
  <div class="flex-1 bg-white rounded-lg shadow-sm border">
    <div class="border-b bg-white p-2 flex justify-between items-center">
      <h2 class="text-lg font-medium text-gray-900">Do on Arrival</h2>
      <button mat-icon-button 
              class="text-blue-600 hover:bg-blue-50" 
              (click)="openDialog()"
              matTooltip="Add item">
        <mat-icon svgIcon="mat_solid:add"></mat-icon>
      </button>
    </div>

    <div class="p-4 h-full overflow-y-auto">
      @if (selectedDoOnArrival.length === 0) {
        <div class="flex flex-col items-center justify-center h-64 text-gray-400">
          <mat-icon class="text-4xl mb-2" svgIcon="mat_solid:assignment"></mat-icon>
          <p class="text-sm">No items added</p>
        </div>
      } @else {
        <div class="space-y-1">
          @for(item of selectedDoOnArrival; track $index){
            <div class="border rounded-lg p-2 hover:bg-gray-50 flex justify-between items-center">
              <div class="flex-1">
                <div class="font-medium text-gray-900">{{ item.CODE }} - {{ item.DESCRIPTION }}</div>
              </div>
              <button mat-icon-button 
                      class="text-gray-400 hover:text-red-500 hover:bg-red-50" 
                      (click)="removeItem($index, 'doOnArrival', item)"
                      matTooltip="Remove">
                <mat-icon svgIcon="mat_solid:close"></mat-icon>
              </button>
            </div>
          }
        </div>
      }
    </div>
  </div>

  <!-- Trace on Arrival Section -->
  <div class="flex-1 bg-white rounded-lg shadow-sm border">
    <div class="border-b bg-white p-2 flex justify-between items-center">
      <h2 class="text-lg font-medium text-gray-900">Trace on Arrival</h2>
      <button mat-icon-button 
              class="text-blue-600 hover:bg-blue-50" 
              (click)="opentraceonDialog()"
              matTooltip="Add item">
        <mat-icon svgIcon="mat_solid:add"></mat-icon>
      </button>
    </div>

    <div class="p-4 h-full overflow-y-auto">
      @if (selectedTraceOnArrival.length === 0) {
        <div class="flex flex-col items-center justify-center h-64 text-gray-400">
          <mat-icon class="text-4xl mb-2" svgIcon="mat_solid:search"></mat-icon>
          <p class="text-sm">No items added</p>
        </div>
      } @else {
        <div class="space-y-1">
          @for (item of selectedTraceOnArrival; track item.IDENTIFIER){
            <div class="border rounded-lg p-2 hover:bg-gray-50 flex justify-between items-center">
              <div class="flex-1">
                <div class="font-medium text-gray-900">{{ item.CODE }} - {{ item.DESCRIPTION }}</div>
              </div>
              <button mat-icon-button 
                      class="text-gray-400 hover:text-red-500 hover:bg-red-50" 
                      (click)="removeItem($index, 'traceOnArrival', item)"
                      matTooltip="Remove">
                <mat-icon svgIcon="mat_solid:close"></mat-icon>
              </button>
            </div>
          }
        </div>
      }
    </div>
  </div>
</div>
