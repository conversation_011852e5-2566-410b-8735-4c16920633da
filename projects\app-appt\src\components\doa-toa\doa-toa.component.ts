import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { RouterLink } from '@angular/router';
import { DeleteDialogComponent, DOADialogComponent,TraceOnarrivalComponent } from '@app-appt/components';
import { FillHeightDirective } from 'ec-ngcore/ui';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MaterialPackage } from '@app-appt/utils';
import { DoaService } from '@app-appt/services/doa.service';

@Component({
  selector: 'his-doatoa',
  standalone: true,
  imports: [CommonModule, FillHeightDirective, MatSnackBarModule, FormsModule,
            MaterialPackage
  ],
  templateUrl: './doa-toa.component.html',
  styleUrl: './doa-toa.component.scss'
})
export class DoatoaComponent {
  selectedDoOnArrival: any[] = [];
  selectedTraceOnArrival: any[] = [];
  identifiers: any;
  identifiers1: any;
  constructor(private dialog: MatDialog, public _arrivalservice: DoaService) { }

  openDialog(): void {
    const dialogRef = this.dialog.open(DOADialogComponent, {
      width: "90vw",
      height: "95vh"
    });

    dialogRef.afterClosed().subscribe(selectedData => {
      if (selectedData) {
        for (const item of selectedData) {
          if (!this.selectedDoOnArrival.some(existingItem => existingItem.IDENTIFIER === item.IDENTIFIER)) {
            this.selectedDoOnArrival.push(item);
          }
        }
        this.identifiers = this.selectedDoOnArrival.map(item => item.IDENTIFIER);
         this.updateArrivalService();
      }
    });
  }


  removeItem(index: number, section: string, data: any): void {
    const dialogRef = this.dialog.open(DeleteDialogComponent, {
      height: '180px',
      width: '280px',
      data: data,
    });
    dialogRef.afterClosed().subscribe(item => {
      if (item == 1) {
        if (section === 'doOnArrival') {
          this.selectedDoOnArrival.splice(index, 1);
          this.identifiers = this.selectedDoOnArrival.map(item => item.IDENTIFIER);

        } else if (section === 'traceOnArrival') {
          this.selectedTraceOnArrival.splice(index, 1);
        }  
        this.updateArrivalService();
      };
    })
  }

  opentraceonDialog(): void {
    const dialogRef = this.dialog.open(TraceOnarrivalComponent, {
      width: "90vw",
      height: "95vh"
    });

    dialogRef.afterClosed().subscribe(selectedData => {
      if (selectedData) {
        for (const item of selectedData) {
          if (!this.selectedTraceOnArrival.some(existingItem => existingItem.IDENTIFIER === item.IDENTIFIER)) {
            this.selectedTraceOnArrival.push(item);
          }
        }
        this.identifiers1 = this.selectedTraceOnArrival.map(item => item.IDENTIFIER);
      }
    });
  }
    private updateArrivalService(): void {
    this._arrivalservice.setarrival({ selectarrival: this.selectedDoOnArrival });
  }
}
