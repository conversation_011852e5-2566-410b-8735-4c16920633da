import { Component, Input, Output, EventEmitter } from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { NgxMaterialIntlTelInputComponent } from 'ngx-material-intl-tel-input';

@Component({
  selector: 'his-common-address',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatOptionModule,
    NgxMaterialIntlTelInputComponent
  ],
  templateUrl: './his-common-address.component.html',
  styleUrls: ['./his-common-address.component.scss']
})
export class HisCommonAddressComponent {
  @Input() addressData: any = {};
  @Input() visitData: any = {};
  @Input() phoneForm?: FormGroup;
  
  @Output() addressChange = new EventEmitter<any>();

  get hasVisitData(): boolean {
    return this.visitData && Object.keys(this.visitData).length > 0;
  }

  get addressTypes(): any[] {
    return this.visitData?.AD || [];
  }

  get countries(): any[] {
    return this.visitData?.CY || [];
  }

  onAddressFieldChange() {
    this.addressChange.emit(this.addressData);
  }
}
