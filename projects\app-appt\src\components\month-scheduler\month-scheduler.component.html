<div class="table-carousel-container">
    <button [ngClass]="{'pointer-arrow': true, 'disabled': isArrowDisabled}" (mouseenter)="enableArrow()"
        (mouseleave)="disableArrow()" alt="arrow" class="arrow left-arrow" (click)="prevPeriod()">&#10094;
    </button>
    <div class="grow">
        <div class="flex  pb-1">
            <div class="justify-start space-x-2">
                <button (click)="setView('week')"
                    [ngClass]="{'bg-blue-500 text-white': currentView === 'week', 'bg-gray-200': currentView !== 'week'}"
                    class="px-4 py-1 rounded">
                    Week
                </button>
                <button (click)="setView('month')"
                    [ngClass]="{'bg-blue-500 text-white': currentView === 'month', 'bg-gray-200': currentView !== 'month'}"
                    class="px-4 py-1 rounded">
                    Month
                </button>
            </div>
            <div class="justify-end ml-auto mt-auto">
                <his-findlistlegend></his-findlistlegend>
            </div>
        </div>
        <div class="calendar-navigation">
           @if (currentView == 'week'){
              <b>{{currentWeekStart | date:'MMM yyyy'}}</b>  
           }
           @else {
              <b>{{currentMonth | date:'MMM yyyy'}}</b>  

           }
        </div>

        <div class="calendar" ecui-height-fill>
            @switch(currentView) {
                @case ('month') {
                   <ng-container *ngTemplateOutlet="monthlyView"></ng-container>
                }
                @default {
                   <ng-container *ngTemplateOutlet="weeklyView"></ng-container>
                }
            }
        </div>
    </div>
    <button [ngClass]="{'pointer-arrow': true, 'disabled': isArrowDisabled}" (mouseenter)="enableArrow()"
        (mouseleave)="disableArrow()" alt="arrow" class="arrow right-arrow" (click)="nextPeriod()">&#10095;
    </button>
</div>

<ng-template #monthlyView>
    <div class="calendar-header pr-2">
        @for (day of ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']; track $index)
        {
           <div class="header-day">{{ day }}</div>
        }
    </div>
    <div class="calendar-body overflow-x-hidden overflow-y-auto">
       @for (week of monthWeeks; track $index){
          <div class="week">
             @for (day of week.days; track $index){
               <div class="day" [class.not-in-month]="!day.inMonth">
                 <div class="date-header flex gap-2 mb-1">{{ day.date | date:'dd' }}
                    @let rdesc = getDescription(day.date);
                    @if (rdesc && rdesc.length){
                        <span [ngClass]="{'description-style': true}"
                            class="justify-center text-center items-center pb-2  mb-1">
                            {{ rdesc }}
                        </span>
                    }
                 </div>
                 <div class="resource-availability mt-1">
                    @for (resource of objsession; track $index){
                        <div>
                        @let rsess = getResourceSessions(resource, day.date);
                        @for (sess of rsess; track $index){
                            <div>
                                <div class="pl-2 pr-2">
                                    <div [style.border-top]="'4px solid ' + (sess.preferredtimeavl > 0 ? '#74c2db' : 'lightred')"
                                        [ngClass]="getButtonClass(sess)"
                                        (click)="slotgeneratepopup(sess)"
                                        matTooltip="{{ 'Start Time: ' + (sess.starttime | slice:0:5) + ', End Time: ' + (sess.endtime | slice:0:5) + ', Slots Available: ' + sess.preferredtimeavl }}"
                                        class="session-button mb-1">
                                        <div class="flex flex-col items-center ">
                                            <span class="text-center font-medium justify-center">
                                                <span class="text-base"> {{ sess.sessiondesc | slice:0:3 }}
                                                </span> - <span class="text-sm">{{ sess.availablepercentage
                                                    }}%</span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                        </div>
                    }
                </div>
               </div>
             }
          </div>
       }
    </div>
</ng-template>

<ng-template #weeklyView>
    <div class="calendar-header">
        @for (day of weekDays; track $index) {
            <div class="header-day flex-col">
                <div class="day-name">{{ day.dayName }}</div>
                <div class="day-number">{{ day.dayNumber }}</div>
                @let rdesc = getDescription(day.date);
                @if (rdesc && rdesc.length){
                    <span [ngClass]="{'description-style': true}"
                        class="ml-auto justify-center text-center items-center pb-2 mb-1">
                        {{ rdesc }}
                    </span>
                }
            </div>
        }
    </div>
    <div class="calendar-body overflow-x-hidden overflow-y-auto">
        <div class="week">
            @for (day of weekDays; track $index){
                <div class="day">
                    <div class="resource-availability">
                        @for (resource of objsession; track $index){
                            <div>
                                @let rsess = getResourceSessions(resource, day.date);
                                @for (sess of rsess; track $index){
                                    <div>
                                        <div class="pl-2 pr-2">
                                            <div [style.border-top]="'4px solid ' + (sess.preferredtimeavl > 0 ? '#74c2db' : 'lightred')"
                                                [ngClass]="getButtonClass(sess)"
                                                (click)="slotgeneratepopup(sess)"
                                                matTooltip="{{ 'Start Time: ' + (sess.starttime | slice:0:5) + ', End Time: ' + (sess.endtime | slice:0:5) + ', Slots Available: ' + sess.preferredtimeavl }}"
                                                class="session-button mb-1">
                                                <div class="flex flex-col items-center">
                                                    <span class="text-center font-medium justify-center">
                                                        <span class="text-base"> {{ sess.sessiondesc | slice:0:3 }}
                                                        </span> - <span class="text-sm">{{ sess.availablepercentage }}%</span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                    </div>
                </div>
            }
        </div>
    </div>
</ng-template>


