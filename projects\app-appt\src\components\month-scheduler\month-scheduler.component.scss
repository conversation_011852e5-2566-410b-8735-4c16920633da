/* CSS Styling for Legend */
.legend {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.legend-color {
  width: 13px;
  height: 14px;
  border-radius: 21%;
  margin-right: 5px;
}

.legend-label {
  font-size: 11px;
  font-weight: bold;
  color: #333;
}

.custom-dropdown {
  position: relative;
  display: inline-block;
  font-size: larger;
  font-weight: 400;
}

.dropdown-selection mat-icon {
  margin-right: 8px;
  /* Adjust spacing as needed */
}

.dropdown-selection input {
  border: none;
  outline: none;
  flex-grow: 1;
  font-size: smaller;
}

.dropdown-selection {
  padding: 14px 0px;
  border: 1px solid #ccc;
  cursor: pointer;
  height: 56px;
  overflow: hidden;
  border-radius: 5px;
  font-size: large;
  font-weight: 400;
  margin-left: -19px;
  padding-left: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.session-button1 {
  display: block;
  width: 100%;
  height: 60px;
  max-height: 100%;
  /* padding: 6px; */
  text-align: center;
}

.dropdown-menu {
  position: absolute;
  background-color: #f7fbfd;
  min-width: 214px;
  border: 1px solid #ccc;
  z-index: 11;
  display: flex;
  max-height: 194px;
  overflow-y: auto;
  margin-left: -17px;
}

.time-column {
  flex: 1;
  overflow-y: auto;
}

.time-column div {
  padding: 10px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
}

.time-column div:hover,
.time-column div.selected {
  background-color: #4d8ea9;
}

/* Style for selected time */
.selected {
  background-color: #007bff;
  color: white;
}

.dropdown-label {
  position: absolute;
  top: -10px;
  left: -10px;
  background-color: #fff;
  padding: 0px 5px;
  font-size: 13px;
  color: rgb(16 15 15 / 54%);
}

.time-range-picker {
  display: flex;
  align-items: center;
  width: 100%;
}

.inputbox.from-time,
.inputbox.to-time {
  margin: 0;
}

.schedule-table {
  width: 100%;
  border-collapse: collapse;
  text-align: left;
  table-layout: fixed;
  /* to ensure uniform cell width */
}

.schedule-table th,
.schedule-table td {
  border: 1px solid #ddd;
  height: 70px;
  vertical-align: baseline;
  text-align: center;
}

.schedule-table th {
  background-color: #f9f9f9;
}

.to-time-separator {
  margin: 0 8px;
  white-space: nowrap;
}

.available-slot {
  background-color: #89d4f6;
  color: white;
  padding: 14px;
}

.fixed {
  width: 1380px;
  overflow: hidden;
}

.date-navigation-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1px;
}

.date-nav-button {
  background-color: #ffffff;
  border: 2px solid #2e4f9c;
  border-radius: 20%;
  padding: 2px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.date-nav-button:hover {
  transform: scale(1.1);
}

.date-nav-button .material-icons {
  font-size: 24px;
}


.booked-slot {
  background-color: #f04e4e;
  color: white;
  padding: 14px;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-gap: 10px;
}

.grid-item button {
  padding: 7px;
  width: 100%;
  box-sizing: border-box;
}

.rounded-2xl {
  border-radius: 0.5rem;
}


.session-button {
  display: block;
  width: 100%;
  /* Sets button width to fill the cell */
  height: 40px;
  /* Optional: Sets button height to fill the cell */
  max-height: 100%;
  /* Removes any default margin // Ensures padding is included in total width/height */
}

.selected {
  background-color: #007bff;
  color: white;
}

.green-button {
  background-color: #c9ecee;
}

.orange-button {
  background-color: orange;
}

.red-button {
  background-color: rgb(246 94 94);
}

.lite-red-button {
  background-color: #f6a1aa;
}

.description-style {
  background-color: #009688;
  color: white;
  padding: 0.5em 2em;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 21px;
  font-size: x-small;
  text-align: center;
}


thead th {
  position: sticky;
  // top: 0;
  padding-top: 20px;
  background-color: #ededed;
  z-index: 2;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
  vertical-align: baseline;
}

table {
  width: 100%;
  border-collapse: collapse;
  max-width: 1200px;
  margin: auto;
}

thead th {
  position: sticky;
  top: 0;
  background-color: #ededed;
  z-index: 2;
  vertical-align: baseline;
}

tbody,
td,
tfoot,
th,
thead,
tr {
  border-color: inherit;
  border-width: 0.01px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 213px;
  text-align: center;
  flex: 1;
  text-align: center;
  // padding: 8px;
  flex: 1;
  text-align: center;
  font-size: 14px;
}

.example-button-row button,
.example-button-row a {
  margin-right: 8px;
}

.right {
  justify-content: right;
  display: flex;
  padding-top: 25px;
}

.right1 {
  justify-content: right;
  display: flex;
  padding-top: 3px;
}

.time-picker {
  position: relative;
}

.time-picker::-webkit-calendar-picker-indicator {
  background-repeat: no-repeat;
  display: inline-block;
  fill: grey;
  height: 20px;
  width: 30px;
}

:host ::ng-deep .mat-form-field-infix {
  display: flex;
}


.example-disabled {
  display: inline-block;
}

.inputbox {
  width: 100%;
  font-weight: bold;
}

.inputbox1 {
  width: 100%;
}

.inputbox2 {
  width: 100%;
}

.matdate {
  height: 49px;
}

.matdate1 {
  height: 49px;
  width: 213px;
}

#datepicket {
  width: 212px;
}

th {
  white-space: nowrap;
  vertical-align: bottom;
  color: black;
}

td div {
  margin-bottom: 4px;
  padding-top: 0px;
  margin-left: 1px;
  margin-right: 1px;
  margin-block: auto;
}

.full-booking {
  background-color: red;
  color: white;
}

.half-booking {
  background-color: orange;
  color: white;
}

.not-available {
  color: rgb(103, 129, 245);
}

.sessions span {
  display: block;
  text-align: center;
  height: 33px;
}

.not-full-booking {
  background-color: green;
  color: white;
}

.holiday-no-booking {
  background-color: #f1f5f9;
  color: white;
}

.sunday {
  background-color: red;
  color: white;
}

.holiday-label {
  color: red;
  font-weight: bold;
  margin-left: 5px;
}

.colheader {
  text-align: center;
  color: #f1f5f9;
}

.medium-blue-button {
  background-color: #b9dbfe;
}

.time-slot-container button {
  background-color: #f2f2f2;
  color: #333;
  border: 1px solid #ccc;
  padding: 10px 20px;
  margin: 5px;
  cursor: pointer;
  font-size: 16px;
  border-radius: 5px;
  outline: none;
  transition: background-color 0.3s, transform 0.3s;
}

.time-slot-container button:hover {
  background-color: #e7e7e7;
  transform: translateY(-2px);
}

.time-slot-container button.selected {
  background-color: #4CAF50;
  color: white;
  border: 1px solid #367a38;
}

.mat-form-field {
  width: 100%;
}

.row-btn {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

th .mat-icon {
  color: white;
}

.table-wrapper {
  position: relative;
  overflow: hidden;
}

.table-container {
  max-height: 515px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  display: block;
}

.arrow {
  cursor: pointer;
  user-select: none;
  padding: 10px;
  font-size: 44px;
  z-index: 10;
  color: #2e4f9c;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  width: 50px;
  margin: 0 10px;
}

.table-carousel-container {
  position: relative;
  align-items: center;
  display: flex;
  justify-content: center;
  overflow-x: auto;
  max-width: 100%;
}

.carousel-control {
  position: absolute;
  top: 50%;
  background-color: #fff;
  border: none;
  cursor: pointer;
  padding: 10px;
  z-index: 3;
  transform: translateY(-50%);
}

.left-arrow {
  left: 0;
}

.right-arrow {
  right: 0;
}

.poiter-arrow {
  cursor: default;
  color: grey;
  ;
}

.poiter-arrow:hover {
  cursor: pointer;
  color: #2e4f9c;
  ;
}

.calendar {
  display: flex;
  flex-direction: column;
  font-family: 'Roboto', sans-serif;
  /* Google Calendar uses Roboto */
  color: #333;
  // width: 1100px;
  margin-bottom: 5px;
  // overflow: hidden;
  font-size: 14px;
}

.calendar-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 45%;
  background-color: #f1f3f4;
  /* Google's UI color */
}

.calendar-header {
  display: flex;
  background-color: #f1f3f4;
  /* consistent with navigation bar */
}

.header-day {
  flex: 1;
  text-align: center;
  font-weight: bold;
  padding: 10px 0;
  border-right: 1px solid #e0e0e0;
  /* subtle separator */
}

.header-day:last-child {
  border-right: none;
}

.calendar-body {
  display: flex;
  flex-direction: column;
}

.week {
  display: flex;
}

.day {
  flex-grow: 1;
  flex-basis: calc(100% / 7);
  border: 1px solid #ddd;
  min-height: 77px;
  /* Adjust the minimum height as needed */
  position: relative;
  background-color: #fff;
}

.date-header {
  position: absolute;
  top: 8px;
  left: 8px;
  font-weight: bold;
  color: #5f6368;
}

.resource-availability {
  padding-top: 30px;
  /* Space for the date */
}

.not-in-month {
  background-color: #ffffff;
  /* Light grey for days not in the current month */
}

.empty-day {
  background-color: transparent;
  /* Adjust if you have a specific style for empty days */
}

.mdc-snackbar__surface {
  background-color: white !important;
  border: none !important;
  border-left: solid 3px #DD2F2F !important;

}

.mat-mdc-snack-bar-label {
  color: #DD2F2F !important;
  font-weight: 600 !important;
}

.view-buttons {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
}

.view-buttons button {
  margin: 0 5px;
  padding: 5px 10px;
  background-color: #2e4f9c;
  color: white;
  border: none;
  cursor: pointer;
}

.view-buttons button:hover {
  background-color: #1a3778;
}

/* Week view specific styles */
.header-day.flex-col {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.day-name {
  font-weight: bold;
  color: #5f6368;
  font-size: 0.9rem;
}

.day-number {
  font-size: 1.2rem;
  color: #202124;
  margin-top: 4px;
}

.description-style {
  font-size: 0.75rem;
  color: #5f6368;
  background-color: #e8f5e9;
  border-radius: 4px;
  padding: 2px 4px;
  margin-top: 4px;
}

.session-button {
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 0.85rem;
}

.session-button-available {
  background-color: #e8f5f9;
  color: #0277bd;
}

.session-button-unavailable {
  background-color: #ffebee;
  color: #c62828;
}