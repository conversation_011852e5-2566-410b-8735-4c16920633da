<form class="form-inline  p-2"  novalidate  >
<div class="flex gap-x-2">
    <mat-form-field subscriptSizing="dynamic" appearance="outline">
        <mat-label>Id Type</mat-label>
        <mat-select name="ID Type" #select [(ngModel)]="idType">
            <mat-option *ngFor="let data of visitData?.ID " [value]="data.IDENTIFIER">{{ data.DESCRIPTION
                }}</mat-option>
        </mat-select>
    </mat-form-field>

    <mat-form-field subscriptSizing="dynamic" appearance="outline">
        <mat-label> IDNo</mat-label>
        <input matInput [(ngModel)]="idValue" name="IDNo" placeholder="Filter IDNo">
    </mat-form-field>

    <mat-form-field subscriptSizing="dynamic" appearance="outline">
        <mat-label> Appointment No.</mat-label>
        <input matInput [(ngModel)]="aptNo" name="IDNo" placeholder="Filter IDNo">
    </mat-form-field>

    <mat-form-field subscriptSizing="dynamic" appearance="outline">
        <mat-label>Clinic</mat-label>
        <input matInput  name="hrn" placeholder="Clinic">
    </mat-form-field>
</div>
  
<div class="flex gap-x-2  pt-2">
    <div class="mr-auto">
        <mat-form-field subscriptSizing="dynamic" appearance="outline">
            <mat-label>From Date - To Date</mat-label>
            <mat-date-range-input [formGroup]="range" [rangePicker]="picker" required>
                <input matStartDate ec-AutoFormatDate formControlName="start" placeholder="MM/dd/yyyy" />
                <input matEndDate ec-AutoFormatDate formControlName="end" placeholder="MM/dd/yyyy" />
            </mat-date-range-input>
            <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-date-range-picker #picker></mat-date-range-picker>
            <mat-error *ngIf="!range.value.start || !range.value.end">
                Date is required.
            </mat-error>
        </mat-form-field>
    </div>

    <button mat-raised-button matTooltip="Search" color="primary" (click)="updatevisitdate()" type="submit">
        Search
    </button>
    <button mat-raised-button matTooltip="Reset" type="reset" color="warn"  (click)="resetsearch()">
        Reset
    </button>
    <button mat-raised-button matTooltip="Appointment" type="button" color="primary" (click)="newappointments()">
     New Appointment
    </button>
</div>
  
</form>