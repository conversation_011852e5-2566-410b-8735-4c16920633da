import { CommonModule, DatePipe, DecimalPipe } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormGroup, FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { SearchPatientDlg } from 'his-components';
import { ApplicationService } from 'ec-ngcore';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MaterialPackage} from '@app-appt/utils';

@Component({
  selector: 'his-searchnew',
  standalone: true,
  imports: [CommonModule,  FormsModule, MaterialPackage,
  ],
  templateUrl: './search-appfilter.component.html',
  styleUrl: './search-appfilter.component.scss',
  providers: [DatePipe, DecimalPipe,
  ]
})
export class SearchnewComponent {
  @Output() filterApplied: EventEmitter<string> = new EventEmitter<string>();
  @Output() visitdate: EventEmitter<string> = new EventEmitter<string>();
  @Output() onSearchPatient: EventEmitter<void> = new EventEmitter<void>();
  @Input() enableSearchPatient: boolean = true;
  @Input() showSearchPatientDlg: boolean = true;
  @Output() new = new EventEmitter<any>();
  @Input() range = new FormGroup({
    start: new FormControl<Date | null>(null),
    end: new FormControl<Date | null>(null),
  });

  @Input() visitData: any

  idValue: string = '';
  aptNo: string = '';
  idType: string = "";
  data:any;

  constructor(
    public _dialog: MatDialog,
    public _appService: ApplicationService) { }

  public resetsearch() {
    this.range = new FormGroup({
      start: new FormControl<Date | null>(null),
      end: new FormControl<Date | null>(null),
    });
  }

  searchPatient() {
    this.onSearchPatient.emit();
    if (this.showSearchPatientDlg) {
      SearchPatientDlg.openDialog(this._dialog, {}).afterClosed().subscribe((result) => { if (result) this.idValue = result.IDNO; this.idType = result.IDTYPE; });;
    }
  }

  updatevisitdate(): void {
    if (!this.range.value.start || !this.range.value.end) {
      this._appService.alertDialog({
        'title': 'Warning Error',
        message: 'DATE is Required.'
      });
    }
    this.data = {
      IdNo: this.idValue,
      IdType: this.idType,
      AptNo: this.aptNo,
    }
    this.visitdate.emit(this.data);
  }
 
  public newappointments(){
    this.new.emit();
  }
}
