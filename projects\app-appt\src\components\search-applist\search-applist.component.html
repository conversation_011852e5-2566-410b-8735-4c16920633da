
<div class="row-table">
    <div class="table-container" id="table">
       <mat-table [dataSource]="appListDataSource" matSort c    lass="mat-elevation">
            <ng-container matColumnDef="Individual_ID">
                <mat-header-cell *matHeaderCellDef> IDNO </mat-header-cell>
                <mat-cell class="" *matCellDef="let element" >
                    <div>
                        <span [matTooltip]="element.IDTYPE_DESC"> {{element.NAME}} </span> 
                        <span class="cursor-pointer text-blue-700 " >{{element.IDNO}}</span>
                    </div>

                </mat-cell>
            </ng-container>
            <ng-container matColumnDef="appno">
                <mat-header-cell *matHeaderCellDef> Appointment No </mat-header-cell>

                <mat-cell class="" *matCellDef="let element">
                    <div>
                    <span class="">{{element.APPOINTMENTNO}}</span><br />
                    <small class="text-secondary">Appt.Date: <span>{{ element.SCHEDULEDATETIME | date:' dd-MM-yyyy' }} | {{element.REQUESTEDTIME | date:'h:mm a'}}</span></small>
                </div>
                </mat-cell>

            </ng-container>


            <ng-container matColumnDef="resource">
                <mat-header-cell *matHeaderCellDef> Doctor   </mat-header-cell>
                <mat-cell *matCellDef="let element">{{element.RESOURCE_DESC}}</mat-cell>
            </ng-container>
            <ng-container matColumnDef="clinic">
                <mat-header-cell *matHeaderCellDef> Clinic  </mat-header-cell>
                <mat-cell *matCellDef="let element">
                    {{element.CLINIC_DESC}}
                </mat-cell>
            </ng-container>
            <ng-container matColumnDef="status">
                <mat-header-cell *matHeaderCellDef> Status  </mat-header-cell>
                <mat-cell *matCellDef="let element">
                    <mat-icon matTooltip="Edit Appointment Details" class="text-blue-600" svgIcon="mat_outline:edit_calendar"></mat-icon>
                </mat-cell>
            </ng-container>

            <mat-header-row *matHeaderRowDef="displayedColumns ;sticky:true" ></mat-header-row>
            <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
        </mat-table>
    </div>
</div>


