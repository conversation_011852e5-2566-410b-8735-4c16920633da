import { CommonModule, DatePipe, DecimalPipe } from '@angular/common';
import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MaterialPackage } from '@app-appt/utils';

@Component({
  selector: 'app-search-applist',
  standalone: true,
  imports: [FormsModule,CommonModule, MatTableModule, MaterialPackage],
  templateUrl: './search-applist.component.html',
  styleUrl: './search-applist.component.scss',
  providers: [DatePipe, DecimalPipe,
  ],
})
export class SearchnewlistComponent {
  appListDataSource:any[] =[];
  originalAppList: any[]=[];
  
  @Output() onCheck = new EventEmitter<any>();
  public _filterValue: any;

  @Input() 
  set filterValue(value: string) {
    if (value){
    this._filterValue = value;
    this.applyFilter();
  }
  }


  
  @Input() set appList(value: any[]) {
    if (value){
    this.originalAppList = value;
    this.appListDataSource =value;
    this.applyFilter();
  }
 }

  @Output() queueData = new EventEmitter<any>();
  @Output() queueView = new EventEmitter<any>();
  queue: any = {};

  visitData: { [key: string]: any } = {};

  displayedColumns: string[] = ['Individual_ID', 'appno', 'resource', 'clinic','status'];

  constructor(public _dialog:MatDialog){}
 
  applyFilter(): void {
    this.appListDataSource.filter = this._filterValue;
  }

  onReset() {
    this.queue = {};
    this.appListDataSource = this.originalAppList;
    this.getpatient();
  }

  getpatient(): void {
    this.queueData.emit(this.queue);
  }

  Oncheck(data:any) {
    this.onCheck.emit(data);
  }

}
