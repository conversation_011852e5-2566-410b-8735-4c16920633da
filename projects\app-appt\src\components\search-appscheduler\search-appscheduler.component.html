<form novalidate (ngSubmit)="Search()" class="flex-1">
    <div>
        <!-- First row of form fields -->
        <div class="flex space-x-1 w-full pl-1 pr-1">
            <mat-form-field appearance="outline" class="w-full font-bold mr-1">
                <mat-label>Resource Type</mat-label>
                <mat-select name="resourcetype" #select class="font-semibold" required
                    [(ngModel)]="objLoad.resourcetype" 
                    (ngModelChange)="Resourcesearch(); resetsearchtable1()"
                     autofocus>
                        @for (data of initAll?.RESOURCETYPES; track $index) {
                            <mat-option [value]="data.IDENTIFIER">
                                {{data.DESCRIPTION }}</mat-option>
                        }
                </mat-select>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="w-full font-bold mr-1">
                <mat-label>Resource</mat-label>
                <input name="searchTerm" matInput [disabled]="!objLoad.resourcetype" [matAutocomplete]="auto"
                     [(ngModel)]="searchTerm" (ngModelChange)="
                    filterResources($event); resetsearchresource()
                " />
                <mat-autocomplete #auto="matAutocomplete" [displayWith]="displayFn.bind(this)">
                    @for(resource of filteredResources; track $index ){
                        <mat-option  [value]="resource">
                            {{ resource.DESCRIPTION }}
                        </mat-option>
                    }
                </mat-autocomplete>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="w-full font-bold mr-1">
                <mat-label>Clinic</mat-label>
                <mat-select name="clinic" class="font-semibold" [disabled]="!objLoad.resourcetype"
                    [(ngModel)]="objLoad.clinic">
                    @for(data of initAll?.CLINICS; track $index){
                        <mat-option [value]="data.IDENTIFIER">{{ data.DESCRIPTION
                        }}</mat-option>
                    }
                </mat-select>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="w-full font-bold mr-1">
                <mat-label>Speciality</mat-label>
                <mat-select name="speciality" class="font-semibold" [(ngModel)]="objLoad.speciality"
                    [disabled]="!objLoad.resourcetype">
                    @for(data of initAll?.SPECIALITY; track $index){
                        <mat-option [value]="data.IDENTIFIER">{{ data.DESCRIPTION
                        }}</mat-option>
                    }
                </mat-select>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="w-full font-bold mr-1">
                <mat-label>Appointment Type</mat-label>
                <mat-select name="appointmenttypes" class="font-semibold" [disabled]="!objLoad.resourcetype"
                    [(ngModel)]="objLoad.appointmenttypes">
                    @for(data of initAll?.APPOINTMENTTYPES; track $index){
                        <mat-option [value]="data.IDENTIFIER">{{
                            data.DESCRIPTION }}</mat-option>
                    }
                </mat-select>
            </mat-form-field>
        </div>
        
        <!-- Second row with date range and time selection -->
        <div class="flex space-x-2 w-full  pl-1 pr-1" (click)="close()">
            <div class="pr-4">
                <mat-form-field appearance="outline" class="w-full font-bold">
                    <mat-label>From date - to date</mat-label>
                    <mat-date-range-input [rangePicker]="picker" [min]="minDate" [formGroup]="range" required>
                        <input matStartDate matInput required placeholder="From Date" formControlName="startDate">
                        <input matEndDate matInput required placeholder="To Date" formControlName="endDate">
                    </mat-date-range-input>
                    <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-date-range-picker #picker></mat-date-range-picker>
                </mat-form-field>
            </div>
            
            <!-- Custom time dropdown -->
            <div class="relative inline-block text-lg font-normal">
                <label class="absolute -top-2.5 -left-2.5 bg-white px-1 text-xs text-gray-500 z-10">From Time - To Time</label>
                <div class="py-4 px-0 border border-gray-300 cursor-pointer h-14 overflow-hidden rounded-md text-lg font-normal -ml-5 pl-2.5 flex justify-center items-center">
                    <input name="displayValue" type="text" [disabled]="!objLoad.resourcetype" [value]="displayValue"
                        (input)="onInput($event)" [maxLength]="13" placeholder="HH : MM - HH : MM" 
                        class="border-none outline-none flex-grow text-base pl-2" />
                    <mat-icon (focusout)="close()" svgIcon="mat_solid:access_time"
                        (click)="toggleDropdown($event)" class="mr-2"></mat-icon>
                </div>
                
                <!-- Dropdown menu -->
                 @let disabled = !objLoad.resourceType ? "pointer-events-none":"";
                 @if (isOpen){
                   <div  [class]='disabled+"absolute bg-blue-50 min-w-54 border border-gray-300 z-10 flex max-h-48 overflow-y-auto -ml-4"'
                     (focusout)="closeDropdown()">
                    <!-- From time column -->
                    <div class="flex-1 overflow-y-auto">
                        @for (time of times; track $index){
                            <div (click)="selectTime(time, true)" 
                                [class]="disabled+'p-2.5 cursor-pointer border-b border-gray-200 hover:bg-blue-600 hover:text-white' + (time === fromTime ? ' bg-blue-500 text-white' : '')">
                                {{ time }}
                            </div>
                        }
                    </div>
                    <!-- To time column -->
                    <div class="flex-1 overflow-y-auto">
                        @for (time of times ; track $index){
                        <div (click)="selectTime(time, false)" 
                            [class]="disabled+'p-2.5 cursor-pointer border-b border-gray-200 hover:bg-blue-600 hover:text-white' + (time === toTime ? ' bg-blue-500 text-white' : '')">
                            {{ time }}
                        </div>
                        }
                    </div>
                  </div>
                 }
            </div>
            
            <!-- Action buttons -->
            <button class="" mat-raised-button color="primary" type="submit">
                Search
            </button>
            <button class="mr-1 ml-auto" mat-raised-button color="warn" type="reset" (click)="resetsearchtable()">
                Reset
            </button>
            <button class="mr-1 " mat-raised-button color="warn" type="button" (click)="backapp()">
                Back
            </button>
        </div>
    </div>
</form>