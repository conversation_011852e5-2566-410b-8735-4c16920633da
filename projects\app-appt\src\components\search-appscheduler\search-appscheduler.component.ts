import { CommonModule, DatePipe, DecimalPipe } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder,  FormsModule,  UntypedFormGroup } from '@angular/forms';

import { MaterialPackage} from '@app-appt/utils';

@Component({
  selector: 'his-search-appschedular',
  standalone: true,
  imports: [ CommonModule, FormsModule, MaterialPackage,
  ],
  templateUrl: './search-appscheduler.component.html',
  styleUrl: './search-appscheduler.component.scss',
  providers: [DatePipe, DecimalPipe,
  ],
})
export class SearchAppSchedulerComponent {
  objLoad: any = {
    resourcetype: '',
    searchTerm: '',
    clinic: '',
    speciality: '',
    appointmenttypes: '',
    resourcesearch: ''
  }
  searchTerm: string = '';
  today = new Date();
  times: string[] = [];
  isOpen: boolean = false;
  fromTime: string = '';
  toTime: string = '';
  displayValue: string = '';
  objectsearch: any;
  filteredResources:any[] = [];
  range:UntypedFormGroup;
  @Output() performSearch: EventEmitter<any> = new EventEmitter<any>();
  @Output() onReset: EventEmitter<void> = new EventEmitter<void>();
  @Output() ResourceSearch: EventEmitter<any> = new EventEmitter<any>();
  @Output() resetRequested = new EventEmitter<void>();
  @Output() back = new EventEmitter<void>();
  @Input() fromDate?: Date;
  @Input() toDate?: Date;
  @Input() initAll: any;
  @Input() resourcesearch: any
  minDate: any;

  constructor(private _api: HttpClient, public _datePipe: DatePipe, private fb: FormBuilder) 
  {
      this.range = this.fb.group({
         startDate: [null],
        endDate: [null]
    });
    const currentYear = new Date();
    this.minDate = new Date();

  }

  ngOnInit() {
    this.range.patchValue({ "startDate": this.fromDate, "endDate": this.toDate });

    this.generateTimes();
    const today = new Date();
  }
  get startDate() { return this.range.get("startDate")?.value; }
  get endDate() { return this.range.get("endDate")?.value; }

  generateTimes(): void {
    for (let i = 0; i < 24; i++) {
      this.times.push(`${i.toString().padStart(2, '0')}:00`);
      this.times.push(`${i.toString().padStart(2, '0')}:30`);
    }
  }
  toggleDropdown(event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }
    this.isOpen = !this.isOpen;
  }

  close() {
    this.isOpen = false;
  }
  selectTime(time: string, isFromTime: boolean): void {
    if (isFromTime) {
      this.fromTime = time;
    } else {
      this.toTime = time;
    }
    this.updateDisplayValue();
  }
  closeDropdown(): void {
    setTimeout(() => {
      this.isOpen = false;
    }, 100);
  }
  updateDisplayValue(): void {
    this.displayValue = `${this.fromTime || 'HH:mm'} - ${this.toTime || 'HH:mm'}`;
    this.objLoad.fromTime = this.fromTime;
    this.objLoad.toTime = this.toTime;
    this.isOpen = false;
  }
  onInput(Event: Event): void {
    let value = (<HTMLInputElement>Event.target).value;
    let numericValue = value.replace(/[^\d]/g, '');
    numericValue = numericValue.slice(0, 11);
    const insertColon = (val:string) => val.length > 2 ? `${val.slice(0, 2)}:${val.slice(2)}` : val;
    let start = numericValue.slice(0, 4);
    let end = numericValue.slice(4);
    start = insertColon(start);
    end = insertColon(end);
    this.objLoad.fromTime = start;
    this.objLoad.toTime = end;
    this.displayValue = start;
    if (end) {
      this.displayValue += ' - ' + end;
    }
  }

  getTimeInAMPMFormat(timeString: string): string {
    const timeParts = timeString.split(':');
    if (timeParts.length === 3) {
      const hours = parseInt(timeParts[0], 10);
      const minutes = parseInt(timeParts[1], 10);
      const seconds = parseInt(timeParts[2].split('+')[0], 10);
      if (!isNaN(hours) && !isNaN(minutes) && !isNaN(seconds)) {
        const date = new Date(2000, 0, 1, hours, minutes, seconds);
        return this._datePipe.transform(date, 'hh:mm a') || '';
      }
    }
    return 'Invalid Date';
  }
  filterResources(value: any): void {
    this.filteredResources = this.searchTerm ? this.performFilter(value) : this.resourcesearch;
  }

  performFilter(value: any): any[] {
    if (!this.resourcesearch) {
      console.warn('resourcesearch is undefined or null');
      return [];
    }

    if (!value) return this.resourcesearch;

    const filterValue = typeof value === 'string' ? value.toLowerCase() : '';
    return this.resourcesearch.filter((option:any) =>
      option.DESCRIPTION.toLowerCase().includes(filterValue)
    );
  }

  displayFn(resource: any): string {
    if (resource) {
      this.objLoad.resource = resource.IDENTIFIER;
      return resource.DESCRIPTION ? resource.DESCRIPTION : '';
    }
    return '';
  }
  public resetsearchtable() {
    this.objLoad.clinic = '';
    this.objLoad.speciality = '';
    this.objLoad.resource = '';
    this.displayValue = '';
    this.objLoad.searchTerm = '';
    this.filteredResources = [];
    this.objLoad.searchTerm = '';
    this.objLoad.appointmenttypes = '';
    this.range.patchValue({
      startDate: null,
      endDate: null
    });
    this.range.reset();
    this.resetRequested.emit();
  }
  public resetsearchtable1() {
    this.objLoad.clinic = '';
    this.objLoad.speciality = '';
    this.objLoad.resource = '';
    this.displayValue = '';
    this.objLoad.searchTerm = '';
    this.filteredResources = [];
    this.objLoad.searchTerm = '';
    this.objLoad.appointmenttypes = '';
    this.resetRequested.emit();
  }

  public resetsearch(): void {
    this.objLoad.clinic = ''
    this.objLoad.speciality = ''
    this.objLoad.resource = ''
    this.displayValue = '';
    this.filteredResources = [];
    this.objLoad.searchTerm = '';
  }
  public resetsearchresource() {
    this.objLoad.clinic = ''
    this.objLoad.speciality = ''
    this.displayValue = '';
    this.objLoad.appointmenttypes = ''
    this.resetRequested.emit();
  }
  public Resourcesearch(): void {
    this.ResourceSearch.emit(this.objLoad.resourcetype);
  }

  public Search(): void {
    let objLoad = {
      resourcetype: this.objLoad.resourcetype,
      searchTerm: this.searchTerm ? this.objLoad.resource : '',
      // searchTerm: this.objLoad.resource,
      clinic: this.objLoad.clinic,
      speciality: this.objLoad.speciality,
      appointmenttypes: this.objLoad.appointmenttypes,
      fromTime: this.displayValue ? this.objLoad.fromTime : "",
      toTime: this.displayValue ? this.objLoad.toTime : "",
      fromDate: this.startDate,
      toDate: this.endDate,
    }

    this.performSearch.emit(objLoad);
  }

  backapp(){
this.back.emit();
  }
}
