    <div class="card w-full border-0 rounded-lg shadow-lg overflow-auto bg-white">
        <div class="card-header flex flex-row items-center justify-between p-2 bg-blue-800 text-white">
            <div class="text-xl font-bold">
                {{ sessiondate }}
            </div>
            <button class="p-1 rounded-md hover:bg-blue-700 transition-colors" (click)="dialogClose()">
                <mat-icon svgIcon="mat_solid:close" class="text-white"></mat-icon>
            </button>
        </div>

        <div class="p-4">
            <div class="grid grid-cols-8 sm:grid-cols-8 md:grid-cols-8 lg:grid-cols-8 gap-1">
                @for(booking of objGetSessionSlotsGet; track $index){
                    <button 
                        class="py-3 rounded-md text-center font-medium transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                        [class.bg-gray-300]="booking.BOOKED == 'Y'"
                        [class.bg-green-600]="booking.selected == 'Y'"
                        [class.bg-gray-100]="booking.BOOKED != 'Y' && booking.selected != 'Y'"
                        [class.text-white]="booking.selected == 'Y'"
                        [class.text-gray-800]="booking.selected != 'Y'"
                        [class.cursor-not-allowed]="booking.BOOKED == 'Y'"
                        [class.hover:bg-gray-100]="booking.BOOKED != 'Y' && booking.selected != 'Y'"
                        (click)="selectSlot(booking, $index)"
                        [disabled]="booking.BOOKED == 'Y'">
                         {{ getFormattedTime(booking.SLOTTIME) }}
                    </button>
                }
            </div>
        </div>

        <div class="flex flex-col items-center p-2 gap-2 border-t border-gray-200">
            <button 
                mat-raised-button 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-1 px-6 rounded-md transition-colors"
                (click)="submit()">
                Submit
            </button>
            
            @if(isError) {
                <div class="text-red-600 font-medium p-2 rounded-md bg-red-50 flex items-center">
                    <mat-icon class="mr-1">error</mat-icon>
                    Please select a slot before submitting
                </div>
            }
        </div>
    </div>