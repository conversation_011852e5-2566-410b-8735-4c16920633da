import { CommonModule, DatePipe } from '@angular/common';
import { Component, ComponentFactoryResolver, EventEmitter, Inject, Input, OnInit, Output, ViewChild, ViewContainerRef } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Router, RouterLink } from '@angular/router';
import { AppointmentformComponent } from '../../appointment-form/appointment-form.component';
import { PatientService } from 'ecmed-api/visitmgmt';
import { AppointmentsAPIService } from 'ecmed-api/appointments';
import { Subscription } from 'rxjs';
import { DateTime } from 'luxon';
import { DateTimeService } from '@app-appt/services';
import { MaterialPackage } from '@app-appt/utils';
import { SlotSelectionService } from '@app-appt/services/slotselectionservices.service';

@Component({
  selector: 'his-slot-selector',
  standalone: true,
  imports: [CommonModule, MaterialPackage],
  templateUrl: './slot-selector.component.html',
  styleUrl: './slot-selector.component.scss',
  providers: [DatePipe, DateTimeService]
})
export class SlotSelectorComponent implements OnInit {

  @ViewChild('AppointmentformComponent', { read: ViewContainerRef }) container!: ViewContainerRef;
  private subscription: Subscription = new Subscription();

  constructor(
    public _dialogRef: MatDialogRef<SlotSelectorComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public router: Router,
    public _datePipe: DatePipe,
    public _HISAPI: AppointmentsAPIService,
    public _API: PatientService,
    public _dialog: MatDialog,
    private componentFactoryResolver: ComponentFactoryResolver,
    private dateTimeService: DateTimeService, private slotSelectionService: SlotSelectionService,
  ) {
    this.sessiondate = data.sessdate;
    this.subscription.add(this.slotSelectionService.loadComponentTrigger$.subscribe(trigger => {
      if (trigger) {
        this.loadAddComponent();
      }
    }));
  }

  @Output() save = new EventEmitter<void>();

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  loadAddComponent() {
    this.container.clear();
    const componentFactory = this.componentFactoryResolver.resolveComponentFactory(AppointmentformComponent);
    this.container.createComponent(componentFactory);
  }

  objload: any = {
    resourceid: '',
    SessionDate: '',
    SessionId: '',
    resource_dec: '',
  };
  sessiondate: any;
  amSlots: any[] = [];
  pmSlots: any[] = [];
  isError = false;
  appResources: any;
  appResourceTypes: any;
  objGetSessionSlotsGet: any = [];
  selectedSlotInfo: any;
  selectedtime: any;
  RequestedTime: any;
  showAppointmentForm: boolean = false;

  ngOnInit(): void {
    // First load resources
    this._HISAPI.appointmentsAPIInitAllGet().subscribe(
      (response: any) => {
        this.appResources = response.RESOURCES;
        this.appResourceTypes = response.RESOURCETYPES;

        // Then load slots after resources are loaded
        this.appointmentsGetSessionSlotsGet(this.data);
      }
    );
  }

  public appointmentsGetSessionSlotsGet(data: any): void {
    if (!data || !data.resourceid) {
      console.error('Resource ID is missing in the data');
      return;
    }

    const obj = {
      id: data.resourceid,
      SessionDate: data.sessdate,
      SessionId: data.sessionid,
    };

    if (!obj.id) {
      console.error('Resource ID is null or undefined');
      return;
    }

    this._HISAPI.appointmentsAPIGetSessionSlotsGet({
      id: obj.id,
      sessionDate: obj.SessionDate,
      sessionId: obj.SessionId
    }).subscribe(
      (result: any) => {
        if (result) {
          console.log(result, 'slot selection');
          this.objGetSessionSlotsGet = result;
          if (this.objGetSessionSlotsGet.length > 0) {
            this.objload.resource_dec = this.objGetSessionSlotsGet[0].RESDESC;
          }
        }
      },
      (error) => {
        console.error('Error fetching session slots:', error);
      }
    );
  }

  getFormattedTime(slotTime: string): string {
    return this.dateTimeService.getFormattedTime(slotTime);
  }

  selectSlot(booking: any, index: number): void {
    this.isError = false;
    this.objGetSessionSlotsGet.forEach((slot: any, idx: number) => { if (idx !== index) slot.selected = 'N'; });
    booking.selected = booking.selected === 'Y' ? 'N' : 'Y';
    this.selectedSlotInfo = `${index + 1}`;
    this.selectedtime = ` ${(booking.SLOTTIME)}`;
    this.RequestedTime = booking.SLOTTIME;
    this.objload = {
      resourceid: this.data.resourceid,
      sessdate: this.data.sessdate,
      sessionid: this.data.sessionid,
      resource_dec: this.objGetSessionSlotsGet[0].RESDESC,
      duration: this.data.defaultduration,
      speciality: booking.SPECIALITYID,
      clinic: booking.CLINICCODE,
      endtime: this.data.endtime,
      starttime: this.data.starttime,
      fromtime: this.data.fromtime,
      totime: this.data.totime,
      totalslots: this.data.totalslots,
      sessiondesc: this.data.sessiondesc,
      specialityid: booking.SPECIALITYID,
      cliniccode: booking.CLINICCODE
    };

    this.slotSelectionService.setSelectedSlot({
      selectedSlotInfo: this.selectedSlotInfo,
      selectedTime: this.selectedtime,
      requestedTime: this.RequestedTime,
      resourceid: this.objload.resourceid,
      sessdate: this.objload.sessdate,
      sessionid: this.objload.sessionid,
      resource_dec: this.objload.resource_dec,
      defaultduration: this.objload.duration,
      speciality: this.objload.speciality,
      endtime: this.objload.endtime,
      starttime: this.objload.starttime,
      fromtime: this.objload.fromtime,
      totime: this.objload.totime,
      totalslots: this.objload.totalslots,
      sessiondesc: this.objload.sessiondesc,
      clinic: this.objload.clinic,
      specialityid: booking.SPECIALITYID,
      cliniccode: booking.CLINICCODE
    });
  }

  getTimeIn24HourFormat(slotTime: string): string {
    const date = new Date(slotTime);
    const hours = date.getUTCHours().toString().padStart(2, '0');
    const minutes = date.getUTCMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }

  getSlotStyle(booking: any): any {
    if (booking.selected == 'Y') {
      return {
        'background-color': '#42C0FB',
        'color': 'black'
      };
    } else if (booking.booked) {
      return {
        'background-color': '#d3d3d3',
        'color': 'black'
      };
    } else {
      return {
        'background-color': '#F1F5F9',
        'color': 'green',
        'border': '1px solid green'
      };
    }
  }

  public dialogClose(): void {
    this._dialogRef.disableClose = false;
    this._dialogRef.close();
  }

  public submit(): void {
    const isAnySlotSelected = this.objGetSessionSlotsGet.some((booking: any) => booking.selected === 'Y');
    if (!isAnySlotSelected) {
      this.isError = true;
      return;
    }
    this._dialogRef.disableClose = false;
    this._dialogRef.close({ event: 'Arrival' });
  }

  openAppointmentForm() {
    this.showAppointmentForm = true;
  }

  closeAppointmentForm() {
    this.showAppointmentForm = false;
  }
}