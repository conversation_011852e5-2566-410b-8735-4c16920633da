import { CommonModule, DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { AppointmentsAPIService } from 'ecmed-api/appointments';
import { PatientService } from 'ecmed-api/visitmgmt';
import { CommonService } from 'his-components';
import { ApplicationService } from 'ec-ngcore';
import { MaterialPackage } from '@app-appt/utils';
import { FormsModule } from '@angular/forms';
import { SlotSelectionService } from '@app-appt/services/slotselectionservices.service';
import { VisitDataService } from '@app-appt/enums';

@Component({
  selector: 'his-slotdetails',
  standalone: true,
  imports: [CommonModule, MaterialPackage, FormsModule],
  templateUrl: './slotdetails.component.html',
  styleUrl: './slotdetails.component.scss',
  providers: [DatePipe]
})

export class SlotdetailsComponent implements OnInit {
  appoitmentvalue: any = {};
  appointmentService: any;
  appSpeciality: any;
  appSubSpeciality: any;
  searchPerformed = false;
  appClinics: any;
  appResources: any;
  appResourceTypes: any;
  appDoctors: any;
  visitData: { [key: string]: any } = {};

  constructor(
    public _router: Router,
    public _HISAPI: AppointmentsAPIService,
    public _API: PatientService,
    public _dialog: MatDialog,
    public commonServices: CommonService,
    private _appService: ApplicationService,
    private slotSelectionService: SlotSelectionService,
    private visitDataService: VisitDataService
  ) { }

  ngOnInit() {
    this.slotSelectionService.selectedSlot$.subscribe(slot => {
      if (slot) {
        this.appointmentService = slot
        this.appoitmentvalue.requestedTime = slot.requestedTime
        this.appoitmentvalue.resource_dec = slot.resource_dec
        this.appoitmentvalue.resourceid = slot.resourceid
        this.appoitmentvalue.selectedSlotInfo = slot.selectedSlotInfo
        this.appoitmentvalue.selectedTime = slot.selectedTime
        this.appoitmentvalue.sessdate = slot.sessdate
        this.appoitmentvalue.sessionid = slot.sessionid
        this.appoitmentvalue.defaultduration = slot.defaultduration
        this.appoitmentvalue.speciality = slot.speciality
        this.appoitmentvalue.clinic = slot.clinic
        // this.appoitmentvalue.selectedSlotInfo = slot.selectedSlotInfo
      }
    });
    this._HISAPI.appointmentsAPIInitAllGet().subscribe(
      (response: any) => {
        this.appSpeciality = response.SPECIALITY;
        this.appSubSpeciality = response.SPECIALITY;
        this.appClinics = response.CLINICS;
        this.appResourceTypes = response.RESOURCETYPES;
        this.appDoctors = response.DOCTORS;
      }
    );
    this.visitDataService.fetchAllVisitData().subscribe(
      (response: any) => {
        this.visitData = response;
      }
    );

    if (!this.appoitmentvalue.resource_dec && !this.appoitmentvalue.sessdate && !this.appoitmentvalue.requestedTime) {
      this._appService.alertDialog({
        'title': 'System Error',
        message: 'Appointment Details Not Found. Please Rebook the Appointment. ',
      }).afterClosed.bind(this._router.navigate(['appointments']))
    }
  }

  getTimeFromDate(datetime: string): string {
    if (typeof datetime === 'string' && datetime.length > 0) {
      datetime = datetime.substring(0, datetime.length - 3);
      const timeString = datetime.split('T')[1];
      return timeString;
    } else {
      return '';
    }
  }
}
