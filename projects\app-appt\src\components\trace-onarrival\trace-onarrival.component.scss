

.button-container {
  text-align: center;
  margin-top: 20px;
}

.dialog-title {
  color: #0075d4;
}

.icon-1 {
  color: white;
}

.item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px;
  border-radius: 4px;
  margin-bottom: 3px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.rowtop {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
  padding: 10px;
}

.item.selected {
  background-color: #0075d4;
  color: white;
  padding: 5px 10px;
  margin-bottom: 3px;
}

.container {
  display: flex;
  background-color: white;
  // position: relative;
}

.proceed-button {
  position: absolute;
  top: 20px;
  right: 10px;
  color: rgb(255, 255, 255);
}

.card {
  flex: 1;
  margin: 0 10px;
  overflow: hidden;
  background-color: white;
  margin-top: 16px;
}

.header {
  display: flex;
  color: #0075d4;
  align-items: center;
  justify-content: space-between;

  background-color: #fff;
  border-bottom: 1px solid #ccc;
}

.header h4 {
  margin: 0;
}

.content {
  overflow-y: auto;
}

.scrollable {
  padding: 10px;
  background-color: #fff;
}

.scrollable1 {
  padding: 10px;
  background-color: #fff;
}

.scrollable :hover {
  background: rgb(221, 245, 240);
  text-decoration: underline;
}

.scrollable1 :hover {
  cursor: pointer;
}


.selected-item:last-child {
  border-bottom: none;
}

.divider {
  width: 10px;
}

.trace-on-arrival .header {
  background-color: #fff;
}

.trace-on-arrival .content {
  background-color: #fff;
}