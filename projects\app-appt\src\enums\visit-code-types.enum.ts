import { Injectable } from '@angular/core';
import { PatientService } from "ecmed-api/visitmgmt";
import { Observable, map, forkJoin } from "rxjs";

/**
 * Visit Code Types Enum
 * Contains all the code types used for fetching visit data
 */
export const VisitCodeTypes = {
  PT: 'PT',
  SX: 'SX', 
  RE: 'RE',
  RC: 'RC',
  BG: 'BG',
  NA: 'NA',
  MS: 'MS',
  TC: 'TC',
  ED: 'ED',
  ON: 'ON',
  ID: 'ID',
  LG: 'LG',
  RT: 'RT',
  AD: 'AD',
  CY: 'CY'
} as const;

export type VisitCodeTypes = typeof VisitCodeTypes[keyof typeof VisitCodeTypes];

/**
 * Get all visit code types as an array
 * @returns Array of all visit code type values
 */
export function getAllVisitCodeTypes(): string[] {
  return Object.values(VisitCodeTypes);
}

@Injectable({
  providedIn: 'root'
})
export class VisitDataService {

  constructor(private _API: PatientService) { }

  /**
   * Fetches visit data for all code types defined in the enum
   * @returns Observable containing the visit data for all code types
   */
  public fetchAllVisitData(): Observable<any> {
    const codeTypes = getAllVisitCodeTypes();
    return this.fetchVisitData(codeTypes);
  }

  /**
   * Fetches visit data for specific code types
   * @param codeTypes Array of code types to fetch data for
   * @returns Observable containing the visit data
   */
  public fetchVisitData(codeTypes: string[]): Observable<any> {
    console.log(codeTypes, "codetypes")
    const observables = codeTypes.map(codeType =>
      this._API.patientInitSearchGet({codes: codeType}).pipe(
        map((data: any) => ({ [codeType]: data?.Codes?.[codeType] }))
      )
    );

    return forkJoin(observables).pipe(
      map((responses: any[]) => Object.assign({}, ...responses))
    );
  }
}
