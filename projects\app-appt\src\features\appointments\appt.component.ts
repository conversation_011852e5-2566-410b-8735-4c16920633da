import { Component, ViewChild, ViewEncapsulation } from '@angular/core';
import { SideNavLayoutComponent, VerticalMenuComponent, INavigationItem } from 'ec-ngcore/ui';
@Component({
    standalone: true,
    templateUrl: './appt.component.html',
    encapsulation: ViewEncapsulation.None,
    imports: [SideNavLayoutComponent, VerticalMenuComponent],
})

export class AppointmentHostComponent {
    get navigationItems(): INavigationItem[] {
        return [];
    }
}

