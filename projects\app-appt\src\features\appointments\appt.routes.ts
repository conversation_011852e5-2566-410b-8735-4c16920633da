import { Route, Routes } from '@angular/router';
import {RenderMode} from '@angular/ssr';
import { AuthGuard, NoAuthGuard } from 'ec-ngcore';
import { SearchlistComponent} from "./components/searchlist/searchlist.component";
import { AppointmentfinalizeComponent } from "./components/appointment-finalize/appointmentfinalize.component";
import { SearchappointmentComponent } from "./components/search-appointment/searchappointment.component";
import { ViewappointmentComponent } from "./components/view-appointment/viewappointment.component";
import { AppointmentHostComponent } from "./appt.component";
import { AppNavigationState } from "ec-ngcore";

export const AppointmentRoutes:Route = {
    path: 'appointments',
    canActivate: [AuthGuard],
    canActivateChild: [AuthGuard],
    component: AppointmentHostComponent,
    children: [
    {
        path: '',
        title: 'Appointments',
        component: SearchlistComponent
    },
    {
        path: 'newapp',
        title: 'New Appointments',
        data:{appNavigationState:AppNavigationState.TransactionMode},
        component: SearchappointmentComponent
    },
    {
        path: 'appointmentfinalize',
        title: 'Appointments',
        data:{appNavigationState:AppNavigationState.TransactionMode},
        component: AppointmentfinalizeComponent
    },
    {
        path: 'view/:id',
        title: 'Appointment View',

        data:{appNavigationState:AppNavigationState.TransactionMode},
        component: ViewappointmentComponent
    },

    ]
};
