import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Output,  } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { FillHeightDirective, SearchLayoutComponent } from 'ec-ngcore/ui';
import { DoatoaComponent } from '@app-appt/components';
import { MaterialPackage } from '@app-appt/utils';


@Component({
  selector: 'app-appointment-arrival',
  standalone: true,
  imports: [FillHeightDirective,CommonModule, MaterialPackage,
            MatSnackBarModule,DoatoaComponent,FormsModule],
  templateUrl: './appointment-arrival.component.html',
  styleUrl: './appointment-arrival.component.scss'
})
export class AppointmentArrivalComponent {

  @Output() discard = new EventEmitter();
  constructor(private router: Router,) { }
  public onDiscard(){
    this.discard.emit("Appointment")
  }
  
  nextpage(){
    this.router.navigate(['appointments/appointmentfinalize'])
  }
}
