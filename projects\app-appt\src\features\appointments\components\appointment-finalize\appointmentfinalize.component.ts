import { CommonModule, DatePipe } from '@angular/common';
import { Component, ViewChild, ViewEncapsulation } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { AppointmentsAPIGetAppointmentDetailsGetRequestParams, AppointmentsAPIService, HisWebapiModelsAppointmentsEditApptModel, PatientCreateNewIndividualPostRequestParams } from 'ecmed-api/appointments';
import { EcmedWebapiModelsPatientmgmtEditIndividualModel, PatientService } from 'ecmed-api/visitmgmt';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatButtonModule } from '@angular/material/button';
import { FillHeightDirective } from 'ec-ngcore/ui';
import { SlotdetailsComponent } from '@app-appt/components/slotdetails/slotdetails.component';
import { AppointmentformComponent } from '@app-appt/components/appointment-form/appointment-form.component';
import { CommonService } from '@his-components/components/patients/common.service';
import { SlotSelectionService } from '@app-appt/services/slotselectionservices.service';
import { VisitDataService } from '@app-appt/enums';

@Component({
  selector: 'app-appointment-finalize',
  standalone: true,
  imports: [SlotdetailsComponent, CommonModule, MatButtonModule,
    AppointmentformComponent],
  templateUrl: './appointmentfinalize.component.html',
  styleUrl: './appointmentfinalize.component.scss',
  providers: [DatePipe,],
  encapsulation: ViewEncapsulation.None
})
export class AppointmentfinalizeComponent {
  @ViewChild(AppointmentformComponent) patientFormComponent?: AppointmentformComponent;
  appSpeciality: any;
  appoitmentvalue: any;
  appSubSpeciality: any;
  searchPerformed = false;
  appClinics: any;
  appResources: any;
  appResourceTypes: any;
  appDoctors: any;
  visitData: { [key: string]: any } = {};
  appointmentview: any;
  Appointmentselected: any;
  appointmentId?: string;
  appointmentService: any;
  patient: any;
  AppointNo: any;
  resultvalue: any;
  finaldata: any;

  constructor(
    public _router: Router, public _HISAPI: AppointmentsAPIService,
    public _API: PatientService,
    public _dialog: MatDialog, public commonServices: CommonService,
    public router: Router,
    public snackbar: MatSnackBar, private slotSelectionService: SlotSelectionService,
    private visitDataService: VisitDataService
    ) { }

  ngOnInit() {
    this.slotSelectionService.selectedSlot$.subscribe(slot => {
      if (slot) {
        this.appoitmentvalue = {
          requestedTime: slot.requestedTime,
          resource_dec: slot.resource_dec,
          resourceid: slot.resourceid,
          selectedSlotInfo: slot.selectedSlotInfo,
          selectedTime: slot.selectedTime,
          sessdate: slot.sessdate,
          sessionid: slot.sessionid,
          defaultduration: slot.defaultduration,
          speciality: slot.speciality,
          clinic: slot.clinic,

        };
      }
    });
    this._HISAPI.appointmentsAPIInitAllGet().subscribe(
      (response: any) => {
        this.appSpeciality = response.SPECIALITY;
        this.appSubSpeciality = response.SPECIALITY;
        this.appClinics = response.CLINICS;
        this.appResourceTypes = response.RESOURCETYPES;
        this.appDoctors = response.DOCTORS;
      }
    );
    this.visitDataService.fetchAllVisitData().subscribe(
      (response: any) => {
        this.visitData = response;
      }
    );
  }

  SearchData(searchData: any) {
    console.log(searchData, 'searchData');
    this._API.patientSearchIndividualGet({
      searchType: searchData.SearchType,
      name: searchData.Name,
      iDNo: searchData.iDNo,
      iDType: searchData.iDType
    }).subscribe(res => {
      this.patient = res;
    });

  }

  savePatient() {
    let result = this.patientFormComponent?.handleValidate()

    if (result) {
      this.snackbar.open(result, "", {
        horizontalPosition: 'right',
        verticalPosition: 'top',
        duration: 2000,
        panelClass: ['custom-snackbar', 'snackbar-success']
      })
    } else {
      this.patientFormComponent?.Finalize();
    }
  }

  handleSave(data: HisWebapiModelsAppointmentsEditApptModel) {
    this._HISAPI.appointmentsAPICreateNewPost({ hisWebapiModelsAppointmentsEditApptModel: data }).subscribe((result: any) => {
      if (result) {
        this.AppointNo = result;
        const dataresult = result.split(',')[1];
        this.resultvalue = dataresult
        const dataAPPID = result.split(',')[0];
        this.appointmentId = dataAPPID;
        this.router.navigate(['appointments/view/' + this.appointmentId]);
      }
    }
    );
  }

  public Appointmentschedule(): void {
    let obj = {
      id: this.appointmentId,
    }
    this._HISAPI.appointmentsAPIGetAppointmentDetailsGet(<AppointmentsAPIGetAppointmentDetailsGetRequestParams>{ id: obj.id }).subscribe((result: any) => {
      if (result) {
        this.appointmentview = result;
        this.Appointmentselected = this.appointmentview[0];
      }
    });
  }
  saveIndividual(data: EcmedWebapiModelsPatientmgmtEditIndividualModel) {
    this._API.patientCreateNewIndividualPost({ ecmedWebapiModelsPatientmgmtEditIndividualModel: data }).subscribe((result: any) => {
      if (result) {
        this.finaldata = result;
      }
    });
  }

  goback() {
    this.router.navigate(['appointments/newapp']);
  }
}
