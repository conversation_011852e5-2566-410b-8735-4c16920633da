<ecui-search-layout>
    <ng-container ecFixedContent>
        <div *ngIf="event == 'Appointment'">
            <div class="pt-2 bg-card">
                <his-search-appschedular (back)="backapp()" (resetRequested)="resetinputbox()" [fromDate]="startDate" [toDate]="endDate"
                    (performSearch)="performSearch($event)" (ResourceSearch)="ResourceSearch($event)"
                    [initAll]="initAll" [resourcesearch]="resourcesearch"></his-search-appschedular>
            </div>
        </div>
    </ng-container>
    <ng-container ecScrollableContent>
        <div class="bg-white">
            <div *ngIf="event == 'Appointment'">
                <his-appointment-scheduler *ngIf="searchPerformed && !resourceBaseSearch" [objectsearch]="objectsearch"
                    [headerdata]="headerdata" [dateStatus]="dateStatus" [objsession]="objsession"
                    (arrival)="openArrival($event)">
                </his-appointment-scheduler>
                <his-monthscheduler *ngIf="searchPerformed && resourceBaseSearch" [objectsearch]="objectsearch"
                    [headerdata]="headerdata" [dateStatus]="dateStatus" [objsession]="objsession"
                    (arrival)="openArrival($event)"></his-monthscheduler>
            </div>
        </div>
    </ng-container>
    <ng-container ecFixedContent>
        <div *ngIf="event =='Arrival'">
            <app-appointment-arrival (discard)="onDiscard($event)"></app-appointment-arrival>
        </div>
    </ng-container>
</ecui-search-layout>