<ecui-search-layout> 
    <ng-container ecFixedContent>    
        <div class="pt-2 bg-card">
            <his-searchnew (filterApplied)="applyFilter($event)" (visitdate)="visitdate($event)" [visitData]="visitData"
            [range]="range" (new)="add()"></his-searchnew>
        </div>
        <div>
            <div class="flex items-center text-white bg-blue-600 h-6">
                <mat-icon class="text-white text-sm" svgIcon="menu"></mat-icon>
                <span class="text-sm">Search Results :</span>
                <span class="text-sm bg-blue-950 pr-1 pl-1 rounded m-1 height-50">{{appList?.length}}</span>
            </div>
        </div>
    </ng-container>
    <ng-container ecScrollableContent >    
        <div class="border-1 ">
            <app-search-applist [appList]="appList" (onCheck)="onCheck($event)"
            [filterValue]="filterValue"></app-search-applist>
        </div>
    </ng-container>
</ecui-search-layout>
