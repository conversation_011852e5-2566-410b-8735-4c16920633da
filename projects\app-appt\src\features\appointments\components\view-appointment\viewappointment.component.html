<div class="flex flex-col h-full bg-gray-50 p-4 print:bg-white">
    <!-- Header with Back and Print buttons -->
    <div class="flex justify-between items-center mb-4 print:hidden">
        <button class="flex items-center text-blue-600 hover:text-blue-800 transition-colors" (click)="goback()">
            <mat-icon svgIcon="mat_solid:arrow_back"></mat-icon>
            <span class="ml-1">Back to Appointments</span>
        </button>
        <button class="flex items-center text-blue-600 hover:text-blue-800 transition-colors" (click)="onPrint()">
            <mat-icon svgIcon="mat_solid:print"></mat-icon>
            <span class="ml-1">Print</span>
        </button>
    </div>

    <!-- Main Appointment View -->
    <div class="flex-1">
        <his-appoitmentview [traceonarrivalview]="traceonarrivalview" [doonarrivalview]="doonarrivalview"
            [viewdetails]="viewdetails" (editindividual)="editindividual($event)" (edit)="edit()">
        </his-appoitmentview>
    </div>
</div>