import { Component } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { AppointmentsAPIService } from 'ecmed-api/appointments';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDialog } from '@angular/material/dialog';
import { CalcHeightDirective, FillHeightDirective } from 'ec-ngcore/ui';
import { AppoitmenteditComponent, AppoitmentviewComponent } from '@app-appt/components';
import { PatientIndividualFormComponent } from 'his-components';
import { MaterialPackage } from '@app-appt/utils';

@Component({
  standalone: true,
  imports: [AppoitmentviewComponent, FillHeightDirective, CommonModule, MatCardModule, MaterialPackage,
    MatIconModule,],
  templateUrl: './viewappointment.component.html',
  styleUrl: './viewappointment.component.scss'
})

export class ViewappointmentComponent {
  id: any;
  editid: any
  viewdetails: any;
  doonarrivalview: any;
  traceonarrivalview: any;
  appointmentdetails: any;
  doonarrival: any;
  traceonarrival: any;
  isPrintClicked: boolean = false;
  constructor(private route: ActivatedRoute, public router: Router, public _HISAPI: AppointmentsAPIService,
    public dialog: MatDialog) { }
  ngOnInit() {
    this.route.params.subscribe(params => {
      const id = params['id'];
      if (id) {
        this.editid = id;
        this.Appointmentschedule(id);
        this.getdoonarrival(id);
        this.gettraceonarrival(id);
      } else {
        console.error('No appointment ID provided in route');
      }
    });
  }

  public Appointmentschedule(id: string): void {
    this._HISAPI.appointmentsAPIGetAppointmentDetailsGet({ id: id }).subscribe((result: any) => {
      if (result) {
        this.appointmentdetails = result;
        this.viewdetails = this.appointmentdetails[0]
      }
    });
  }

  public getdoonarrival(id: string): void {  // Specify string type
    if (!id) {
      console.error('Invalid ID provided to getdoonarrival');
      return;
    }

    this._HISAPI.appointmentsAPIGetDOADetailsGet({ id: id }).subscribe({
      next: (result: any) => {
        if (result) {
          this.doonarrival = result;
          this.doonarrivalview = this.doonarrival;
          console.log(this.doonarrivalview, 'id doonarrivalview');
        }
      },
      error: (err) => {
        console.error('Error fetching DOA details:', err);
      }
    });
  }

  public gettraceonarrival(id: any): void {
    this._HISAPI.appointmentsAPIGetTOADetailsGet({ id: id }).subscribe((result: any) => {
      if (result) {
        this.traceonarrival = result;
        this.traceonarrivalview = this.traceonarrival;
        console.log(this.traceonarrivalview, 'id gettraceonarrival');
      }
    });
  }


  onPrint() {
    this.isPrintClicked = true;
    window.print();
  }
  editindividual(event: any) {
    const dialogRef = this.dialog.open(PatientIndividualFormComponent, {
      width: "2500px",
      height: "850px",
      panelClass: "custom-dialog-container",
    });
    dialogRef.afterClosed().subscribe(result => {
    });
  }
  edit() {
    const dialogRef = this.dialog.open(AppoitmenteditComponent, {
      width: '500px',
    });
    dialogRef.afterClosed().subscribe(result => {
    });
  }
  goback() {
    this.router.navigate(['appointments/']);
  }
}
