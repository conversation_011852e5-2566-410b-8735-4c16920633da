import { Routes } from "@angular/router";
import { AppointmentfinalizeComponent } from "./components/appointment-finalize/appointmentfinalize.component";
import { SearchappointmentComponent } from "./components/search-appointment/searchappointment.component";
import { ViewappointmentComponent } from "./components/view-appointment/viewappointment.component";
import { SearchlistComponent } from "./components/searchlist/searchlist.component";
import { AppNavigationState } from "ec-ngcore";


export default [
    {
        path: '',
        title: 'Appointments',
        component: SearchlistComponent
    },
    {
        path: 'newapp',
        title: 'New Appointments',
        data:{appNavigationState:AppNavigationState.TransactionMode},
        component: SearchappointmentComponent
    },
    {
        path: 'appointmentfinalize',
        title: 'Appointments',
        data:{appNavigationState:AppNavigationState.TransactionMode},
        component: AppointmentfinalizeComponent
    },
    {
        path: 'view/:id',
        title: 'Appointment View',
        data:{appNavigationState:AppNavigationState.TransactionMode},
        component: ViewappointmentComponent
    },

] as Routes;