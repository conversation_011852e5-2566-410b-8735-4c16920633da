import { Injectable } from '@angular/core';
import { DatePipe } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class DateTimeService {
  constructor(private _datePipe: DatePipe) {}

  public formatTimeSlot(slotTime: string): string {
    if (!slotTime) return slotTime;
    
    // Check if the string ends with Z or contains +
    if (slotTime.endsWith('Z') || slotTime.includes('+')) {
      return slotTime;
    }
    
    // Otherwise add Z at the end
    return slotTime + 'Z';
  }

  public getFormattedTime(slotTime: string): string {
    const formattedSlot = this.formatTimeSlot(slotTime);
    return this._datePipe.transform(formattedSlot, 'shortTime') || '';
  }
}