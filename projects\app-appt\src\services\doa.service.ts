import { Injectable } from "@angular/core";
import { BehaviorSubject } from "rxjs";

@Injectable({
    providedIn: 'root'
})
export class DoaService {
    private arrivalSource = new BehaviorSubject<any>(null);
    arrival$ = this.arrivalSource.asObservable();

    private loadComponentTrigger = new BehaviorSubject<boolean>(false);
    loadComponentTrigger$ = this.loadComponentTrigger.asObservable();

    constructor() { } 

    setarrival(slot: any) {
        console.log(slot, "slot slot slot");
        this.arrivalSource.next(slot);
    }

    triggerComponentLoad(trigger: boolean) {

        this.loadComponentTrigger.next(trigger);
    }

}
