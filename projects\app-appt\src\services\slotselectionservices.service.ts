import { Injectable } from "@angular/core";
import { BehaviorSubject } from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class SlotSelectionService {
  private selectedSlotSource = new BehaviorSubject<any>(null);
  selectedSlot$ = this.selectedSlotSource.asObservable();

  // Add a trigger for component load
  private loadComponentTrigger = new BehaviorSubject<boolean>(false);
  loadComponentTrigger$ = this.loadComponentTrigger.asObservable();

  constructor() { }

  setSelectedSlot(slot: any) {
    console.log(slot, 'slot SlotSelectionService');
    this.selectedSlotSource.next(slot);
  }

  triggerComponentLoad(trigger: boolean) {
    this.loadComponentTrigger.next(trigger);
  }

}
