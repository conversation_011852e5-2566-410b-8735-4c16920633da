import { Injectable } from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { BehaviorSubject } from "rxjs";

@Injectable({
    providedIn: 'root'
})
export class TraceOnarrivalService {
    private traceonarrivalSource = new BehaviorSubject<any>(null);
    traceonarrival$ = this.traceonarrivalSource.asObservable();

    private loadComponentTrigger = new BehaviorSubject<boolean>(false);
    loadComponentTrigger$ = this.loadComponentTrigger.asObservable();

    constructor(public dialog: MatDialog) { }

    settraceonarrival(slot: any) {
        console.log(slot, 'slot TraceOnarrivalService');

        this.traceonarrivalSource.next(slot);
    }

    triggerComponentLoad(trigger: boolean) {

        this.loadComponentTrigger.next(trigger);
    }

}
