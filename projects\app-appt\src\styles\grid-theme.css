ecui-grid 
{

.ecui-grid-editors
{
    display:none;
}

.ecg-table-sticky {
  /* Note that the table can either set this class or an inline style to make something sticky.
   We set the style as `!important` so that we get an identical specificity in both cases
   and to avoid cases where user styles have a higher specificity.
  */
  position: sticky !important;
}

/* Cells need to inherit their background in order to overlap each other when sticky.
 The background needs to be inherited from the table, tbody/tfoot, row, and cell.
*/
.ecg-table tbody, .ecg-table tfoot, .ecg-table thead,
.ecg-cell, .ecg-footer-cell,
.ecg-header-row, .ecg-row, .ecg-footer-row,
.ecg-table .ecg-header-cell {
  background: inherit;
}

.ecui-grid-container
{
    width:100%;
    height:100%;
    overflow:auto;
    border-style:solid;
    border-width:0.6667px;
    border-color:#dee2e6;
    padding:0;
    margin:0;
}
td.ecg-valerror{
   cursor:pointer;
   color:red;
   background-repeat:no-repeat !important;
   background-position:left !important;
   background-image:url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="%23ff0000"><path d="M480-280q17 0 28.5-11.5T520-320q0-17-11.5-28.5T480-360q-17 0-28.5 11.5T440-320q0 17 11.5 28.5T480-280Zm-40-160h80v-240h-80v240Zm40 360q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/></svg>') !important;
}

td.ecg-editing{
    border-bottom:1px solid blue !important;
}
td.ecg-editing div[ecg-cell-content]{
    display:none;
}
div[ecg-cell-content] 
{ 
    display:block;
    width:100% !important;
    z-index:1;
}
table.ecui-grid {
    background-color: white;
    vertical-align: middle;
    empty-cells: show;
    min-width:100%;
    border-collapse:collapse;
    border-style:solid;
    border-width:0;
    line-height: 1.5rem;
}
table.ecui-grid thead {
    vertical-align: middle;
}
table.ecui-grid thead, tbody, tfoot, tr, td, th{
    border-color:inherit;
    border-style:solid;
    border-width:0.6667px;
    border-color:#dee2e6;
}

th.ecg-header-cell {
   font-weight:600;
}
table.ecui-grid thead TR TH {
    min-height: 1.5rem;
    cursor: default;
    padding-right: 0.25rem;
    padding-left: 0.25rem;
}

table.ecui-grid tbody TR TD {
    min-height: 1.5rem;
    white-space: normal;
    border-collapse: collapse;
    width: auto;
    padding-right: 0.25rem;
    padding-left: 0.25rem;
    height:auto !important;
}

.ecg-input-editor
{
   height: 100%;
   width: 100%;
   display:block;
   -moz-box-sizing: border-box;
   -webkit-box-sizing: border-box;
   box-sizing: border-box;
   border: 0px;
   background:white;
}
.ecg-autocomplete-editor
{
   height: 100%;
   width: 100%;
   display:block;
   -moz-box-sizing: border-box;
   -webkit-box-sizing: border-box;
   box-sizing: border-box;
   border: 0px;
   background:white;
}

.ecg-select-editor
{
   height: 100%;
   width: 100%;
   display:block;
   -moz-box-sizing: border-box;
   -webkit-box-sizing: border-box;
   box-sizing: border-box;
   border: 0px;
   background:white;
}

ecg-matselect-editor
{
   > div {
      display:block;
      min-height:100%;
      width:100%;
   }
   .mat-mdc-form-field {min-height:100%; width:100%;}
   .mat-mdc-text-field-wrapper {border:0 !important; padding:0px !important;}
   mat-select
   {
      height:100%;
   }
}
}
