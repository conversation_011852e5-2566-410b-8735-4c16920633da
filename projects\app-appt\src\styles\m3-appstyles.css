html {
  --mat-app-background-color: var(--mat-sys-background);
  --mat-app-text-color: var(--mat-sys-on-background);
  --mat-app-elevation-shadow-level-0: 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-1: 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-2: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-3: 0px 3px 3px -2px rgba(0, 0, 0, 0.2), 0px 3px 4px 0px rgba(0, 0, 0, 0.14), 0px 1px 8px 0px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-4: 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-5: 0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 5px 8px 0px rgba(0, 0, 0, 0.14), 0px 1px 14px 0px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-6: 0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-7: 0px 4px 5px -2px rgba(0, 0, 0, 0.2), 0px 7px 10px 1px rgba(0, 0, 0, 0.14), 0px 2px 16px 1px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-8: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-9: 0px 5px 6px -3px rgba(0, 0, 0, 0.2), 0px 9px 12px 1px rgba(0, 0, 0, 0.14), 0px 3px 16px 2px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-10: 0px 6px 6px -3px rgba(0, 0, 0, 0.2), 0px 10px 14px 1px rgba(0, 0, 0, 0.14), 0px 4px 18px 3px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-11: 0px 6px 7px -4px rgba(0, 0, 0, 0.2), 0px 11px 15px 1px rgba(0, 0, 0, 0.14), 0px 4px 20px 3px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-12: 0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-13: 0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 13px 19px 2px rgba(0, 0, 0, 0.14), 0px 5px 24px 4px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-14: 0px 7px 9px -4px rgba(0, 0, 0, 0.2), 0px 14px 21px 2px rgba(0, 0, 0, 0.14), 0px 5px 26px 4px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-15: 0px 8px 9px -5px rgba(0, 0, 0, 0.2), 0px 15px 22px 2px rgba(0, 0, 0, 0.14), 0px 6px 28px 5px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-16: 0px 8px 10px -5px rgba(0, 0, 0, 0.2), 0px 16px 24px 2px rgba(0, 0, 0, 0.14), 0px 6px 30px 5px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-17: 0px 8px 11px -5px rgba(0, 0, 0, 0.2), 0px 17px 26px 2px rgba(0, 0, 0, 0.14), 0px 6px 32px 5px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-18: 0px 9px 11px -5px rgba(0, 0, 0, 0.2), 0px 18px 28px 2px rgba(0, 0, 0, 0.14), 0px 7px 34px 6px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-19: 0px 9px 12px -6px rgba(0, 0, 0, 0.2), 0px 19px 29px 2px rgba(0, 0, 0, 0.14), 0px 7px 36px 6px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-20: 0px 10px 13px -6px rgba(0, 0, 0, 0.2), 0px 20px 31px 3px rgba(0, 0, 0, 0.14), 0px 8px 38px 7px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-21: 0px 10px 13px -6px rgba(0, 0, 0, 0.2), 0px 21px 33px 3px rgba(0, 0, 0, 0.14), 0px 8px 40px 7px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-22: 0px 10px 14px -6px rgba(0, 0, 0, 0.2), 0px 22px 35px 3px rgba(0, 0, 0, 0.14), 0px 8px 42px 7px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-23: 0px 11px 14px -7px rgba(0, 0, 0, 0.2), 0px 23px 36px 3px rgba(0, 0, 0, 0.14), 0px 9px 44px 8px rgba(0, 0, 0, 0.12);
  --mat-app-elevation-shadow-level-24: 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12);
  --mat-ripple-color: color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent);
  --mat-option-focus-state-layer-color: color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent);
  --mat-option-hover-state-layer-color: color-mix(in srgb, var(--mat-sys-on-surface) 8%, transparent);
  --mat-option-label-text-color: var(--mat-sys-on-surface);
  --mat-option-selected-state-label-text-color: var(--mat-sys-on-secondary-container);
  --mat-option-selected-state-layer-color: var(--mat-sys-secondary-container);
  --mat-optgroup-label-text-color: var(--mat-sys-on-surface-variant);
  --mat-pseudo-checkbox-full-disabled-selected-checkmark-color: var(--mat-sys-surface);
  --mat-pseudo-checkbox-full-disabled-selected-icon-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-pseudo-checkbox-full-disabled-unselected-icon-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-pseudo-checkbox-full-selected-checkmark-color: var(--mat-sys-on-primary);
  --mat-pseudo-checkbox-full-selected-icon-color: var(--mat-sys-primary);
  --mat-pseudo-checkbox-full-unselected-icon-color: var(--mat-sys-on-surface-variant);
  --mat-pseudo-checkbox-minimal-disabled-selected-checkmark-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-pseudo-checkbox-minimal-selected-checkmark-color: var(--mat-sys-primary);
  --mat-option-label-text-size: var(--mat-sys-body-large-size);
  --mat-option-label-text-weight: var(--mat-sys-body-large-weight);
  --mat-option-label-text-font: var(--mat-sys-label-large-font);
  --mat-option-label-text-line-height: var(--mat-sys-label-large-line-height);
  --mat-option-label-text-tracking: var(--mat-sys-label-large-tracking);
  --mat-optgroup-label-text-font: var(--mat-sys-title-small-font);
  --mat-optgroup-label-text-line-height: var(--mat-sys-title-small-line-height);
  --mat-optgroup-label-text-size: var(--mat-sys-title-small-size);
  --mat-optgroup-label-text-tracking: var(--mat-sys-title-small-tracking);
  --mat-optgroup-label-text-weight: var(--mat-sys-title-small-weight);
  --mat-card-elevated-container-shape: 12px;
  --mat-card-filled-container-shape: 12px;
  --mat-card-outlined-container-shape: 12px;
  --mat-card-outlined-outline-width: 1px;
  --mat-card-elevated-container-color: var(--mat-sys-surface-container-low);
  --mat-card-elevated-container-elevation: 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);
  --mat-card-filled-container-color: var(--mat-sys-surface-container-highest);
  --mat-card-filled-container-elevation: 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);
  --mat-card-outlined-container-color: var(--mat-sys-surface);
  --mat-card-outlined-container-elevation: 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);
  --mat-card-outlined-outline-color: var(--mat-sys-outline-variant);
  --mat-card-subtitle-text-color: var(--mat-sys-on-surface);
  --mat-card-subtitle-text-font: var(--mat-sys-title-medium-font);
  --mat-card-subtitle-text-line-height: var(--mat-sys-title-medium-line-height);
  --mat-card-subtitle-text-size: var(--mat-sys-title-medium-size);
  --mat-card-subtitle-text-tracking: var(--mat-sys-title-medium-tracking);
  --mat-card-subtitle-text-weight: var(--mat-sys-title-medium-weight);
  --mat-card-title-text-font: var(--mat-sys-title-large-font);
  --mat-card-title-text-line-height: var(--mat-sys-title-large-line-height);
  --mat-card-title-text-size: var(--mat-sys-title-large-size);
  --mat-card-title-text-tracking: var(--mat-sys-title-large-tracking);
  --mat-card-title-text-weight: var(--mat-sys-title-large-weight);
  --mat-progress-bar-active-indicator-height: 4px;
  --mat-progress-bar-track-height: 4px;
  --mat-progress-bar-track-shape: 0;
  --mat-progress-bar-active-indicator-color: var(--mat-sys-primary);
  --mat-progress-bar-track-color: var(--mat-sys-surface-variant);
  --mat-tooltip-container-color: var(--mat-sys-inverse-surface);
  --mat-tooltip-container-shape: 4px;
  --mat-tooltip-supporting-text-color: var(--mat-sys-inverse-on-surface);
  --mat-tooltip-supporting-text-font: var(--mat-sys-body-small-font);
  --mat-tooltip-supporting-text-line-height: var(--mat-sys-body-small-line-height);
  --mat-tooltip-supporting-text-size: var(--mat-sys-body-small-size);
  --mat-tooltip-supporting-text-tracking: var(--mat-sys-body-small-tracking);
  --mat-tooltip-supporting-text-weight: var(--mat-sys-body-small-weight);
  --mat-form-field-filled-active-indicator-height: 1px;
  --mat-form-field-filled-focus-active-indicator-height: 2px;
  --mat-form-field-filled-container-shape: 4px;
  --mat-form-field-outlined-outline-width: 1px;
  --mat-form-field-outlined-focus-outline-width: 2px;
  --mat-form-field-outlined-container-shape: 4px;
  --mat-form-field-disabled-input-text-placeholder-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-form-field-disabled-leading-icon-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-form-field-disabled-select-arrow-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-form-field-disabled-trailing-icon-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-form-field-enabled-select-arrow-color: var(--mat-sys-on-surface-variant);
  --mat-form-field-error-focus-trailing-icon-color: var(--mat-sys-error);
  --mat-form-field-error-hover-trailing-icon-color: var(--mat-sys-on-error-container);
  --mat-form-field-error-text-color: var(--mat-sys-error);
  --mat-form-field-error-trailing-icon-color: var(--mat-sys-error);
  --mat-form-field-filled-active-indicator-color: var(--mat-sys-on-surface-variant);
  --mat-form-field-filled-caret-color: var(--mat-sys-primary);
  --mat-form-field-filled-container-color: var(--mat-sys-surface-variant);
  --mat-form-field-filled-disabled-active-indicator-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-form-field-filled-disabled-container-color: color-mix(in srgb, var(--mat-sys-on-surface) 4%, transparent);
  --mat-form-field-filled-disabled-input-text-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-form-field-filled-disabled-label-text-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-form-field-filled-error-active-indicator-color: var(--mat-sys-error);
  --mat-form-field-filled-error-caret-color: var(--mat-sys-error);
  --mat-form-field-filled-error-focus-active-indicator-color: var(--mat-sys-error);
  --mat-form-field-filled-error-focus-label-text-color: var(--mat-sys-error);
  --mat-form-field-filled-error-hover-active-indicator-color: var(--mat-sys-on-error-container);
  --mat-form-field-filled-error-hover-label-text-color: var(--mat-sys-on-error-container);
  --mat-form-field-filled-error-label-text-color: var(--mat-sys-error);
  --mat-form-field-filled-focus-active-indicator-color: var(--mat-sys-primary);
  --mat-form-field-filled-focus-label-text-color: var(--mat-sys-primary);
  --mat-form-field-filled-hover-active-indicator-color: var(--mat-sys-on-surface);
  --mat-form-field-filled-hover-label-text-color: var(--mat-sys-on-surface-variant);
  --mat-form-field-filled-input-text-color: var(--mat-sys-on-surface);
  --mat-form-field-filled-input-text-placeholder-color: var(--mat-sys-on-surface-variant);
  --mat-form-field-filled-label-text-color: var(--mat-sys-on-surface-variant);
  --mat-form-field-focus-select-arrow-color: var(--mat-sys-primary);
  --mat-form-field-focus-state-layer-opacity: 0;
  --mat-form-field-hover-state-layer-opacity: 0.08;
  --mat-form-field-leading-icon-color: var(--mat-sys-on-surface-variant);
  --mat-form-field-outlined-caret-color: var(--mat-sys-primary);
  --mat-form-field-outlined-disabled-input-text-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-form-field-outlined-disabled-label-text-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-form-field-outlined-disabled-outline-color: color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent);
  --mat-form-field-outlined-error-caret-color: var(--mat-sys-error);
  --mat-form-field-outlined-error-focus-label-text-color: var(--mat-sys-error);
  --mat-form-field-outlined-error-focus-outline-color: var(--mat-sys-error);
  --mat-form-field-outlined-error-hover-label-text-color: var(--mat-sys-on-error-container);
  --mat-form-field-outlined-error-hover-outline-color: var(--mat-sys-on-error-container);
  --mat-form-field-outlined-error-label-text-color: var(--mat-sys-error);
  --mat-form-field-outlined-error-outline-color: var(--mat-sys-error);
  --mat-form-field-outlined-focus-label-text-color: var(--mat-sys-primary);
  --mat-form-field-outlined-focus-outline-color: var(--mat-sys-primary);
  --mat-form-field-outlined-hover-label-text-color: var(--mat-sys-on-surface);
  --mat-form-field-outlined-hover-outline-color: var(--mat-sys-on-surface);
  --mat-form-field-outlined-input-text-color: var(--mat-sys-on-surface);
  --mat-form-field-outlined-input-text-placeholder-color: var(--mat-sys-on-surface-variant);
  --mat-form-field-outlined-label-text-color: var(--mat-sys-on-surface-variant);
  --mat-form-field-outlined-outline-color: var(--mat-sys-outline);
  --mat-form-field-select-disabled-option-text-color: color-mix(in srgb, #1a1b1f 38%, transparent);
  --mat-form-field-select-option-text-color: #1a1b1f;
  --mat-form-field-state-layer-color: var(--mat-sys-on-surface);
  --mat-form-field-trailing-icon-color: var(--mat-sys-on-surface-variant);
  --mat-form-field-container-height: 56px;
  --mat-form-field-filled-label-display: block;
  --mat-form-field-container-vertical-padding: 16px;
  --mat-form-field-filled-with-label-container-padding-top: 24px;
  --mat-form-field-filled-with-label-container-padding-bottom: 8px;
  --mat-form-field-container-text-font: var(--mat-sys-body-large-font);
  --mat-form-field-container-text-line-height: var(--mat-sys-body-large-line-height);
  --mat-form-field-container-text-size: var(--mat-sys-body-large-size);
  --mat-form-field-container-text-tracking: var(--mat-sys-body-large-tracking);
  --mat-form-field-container-text-weight: var(--mat-sys-body-large-weight);
  --mat-form-field-subscript-text-font: var(--mat-sys-body-small-font);
  --mat-form-field-subscript-text-line-height: var(--mat-sys-body-small-line-height);
  --mat-form-field-subscript-text-size: var(--mat-sys-body-small-size);
  --mat-form-field-subscript-text-tracking: var(--mat-sys-body-small-tracking);
  --mat-form-field-subscript-text-weight: var(--mat-sys-body-small-weight);
  --mat-form-field-outlined-label-text-font: var(--mat-sys-body-large-font);
  --mat-form-field-outlined-label-text-size: var(--mat-sys-body-large-size);
  --mat-form-field-outlined-label-text-tracking: var(--mat-sys-body-large-tracking);
  --mat-form-field-outlined-label-text-weight: var(--mat-sys-body-large-weight);
  --mat-form-field-filled-label-text-font: var(--mat-sys-body-large-font);
  --mat-form-field-filled-label-text-size: var(--mat-sys-body-large-size);
  --mat-form-field-filled-label-text-tracking: var(--mat-sys-body-large-tracking);
  --mat-form-field-filled-label-text-weight: var(--mat-sys-body-large-weight);
  --mat-select-container-elevation-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
  --mat-select-panel-background-color: var(--mat-sys-surface-container);
  --mat-select-enabled-trigger-text-color: var(--mat-sys-on-surface);
  --mat-select-disabled-trigger-text-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-select-placeholder-text-color: var(--mat-sys-on-surface-variant);
  --mat-select-enabled-arrow-color: var(--mat-sys-on-surface-variant);
  --mat-select-disabled-arrow-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-select-focused-arrow-color: var(--mat-sys-primary);
  --mat-select-invalid-arrow-color: var(--mat-sys-error);
  --mat-select-arrow-transform: translateY(-8px);
  --mat-select-trigger-text-font: var(--mat-sys-body-large-font);
  --mat-select-trigger-text-line-height: var(--mat-sys-body-large-line-height);
  --mat-select-trigger-text-size: var(--mat-sys-body-large-size);
  --mat-select-trigger-text-tracking: var(--mat-sys-body-large-tracking);
  --mat-select-trigger-text-weight: var(--mat-sys-body-large-weight);
  --mat-autocomplete-container-shape: 4px;
  --mat-autocomplete-container-elevation-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
  --mat-autocomplete-background-color: var(--mat-sys-surface-container);
  --mat-dialog-actions-alignment: flex-end;
  --mat-dialog-actions-padding: 16px 24px;
  --mat-dialog-container-elevation-shadow: none;
  --mat-dialog-container-max-width: 560px;
  --mat-dialog-container-min-width: 280px;
  --mat-dialog-container-shape: 28px;
  --mat-dialog-container-small-max-width: calc(100vw - 32px);
  --mat-dialog-content-padding: 20px 24px;
  --mat-dialog-headline-padding: 6px 24px 13px;
  --mat-dialog-with-actions-content-padding: 20px 24px 0;
  --mat-dialog-container-color: var(--mat-sys-surface);
  --mat-dialog-subhead-color: var(--mat-sys-on-surface);
  --mat-dialog-supporting-text-color: var(--mat-sys-on-surface-variant);
  --mat-dialog-subhead-font: var(--mat-sys-headline-small-font);
  --mat-dialog-subhead-line-height: var(--mat-sys-headline-small-line-height);
  --mat-dialog-subhead-size: var(--mat-sys-headline-small-size);
  --mat-dialog-subhead-tracking: var(--mat-sys-headline-small-tracking);
  --mat-dialog-subhead-weight: var(--mat-sys-headline-small-weight);
  --mat-dialog-supporting-text-font: var(--mat-sys-body-medium-font);
  --mat-dialog-supporting-text-line-height: var(--mat-sys-body-medium-line-height);
  --mat-dialog-supporting-text-size: var(--mat-sys-body-medium-size);
  --mat-dialog-supporting-text-tracking: var(--mat-sys-body-medium-tracking);
  --mat-dialog-supporting-text-weight: var(--mat-sys-body-medium-weight);
  --mat-chip-container-shape-radius: 8px;
  --mat-chip-disabled-container-opacity: 1;
  --mat-chip-elevated-container-color: transparent;
  --mat-chip-flat-selected-outline-width: 0;
  --mat-chip-outline-width: 1px;
  --mat-chip-trailing-action-focus-opacity: 1;
  --mat-chip-trailing-action-opacity: 1;
  --mat-chip-with-avatar-avatar-shape-radius: 24px;
  --mat-chip-with-avatar-avatar-size: 24px;
  --mat-chip-with-avatar-disabled-avatar-opacity: 0.38;
  --mat-chip-with-icon-disabled-icon-opacity: 0.38;
  --mat-chip-with-icon-icon-size: 18px;
  --mat-chip-with-trailing-icon-disabled-trailing-icon-opacity: 0.38;
  --mat-chip-disabled-label-text-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-chip-disabled-outline-color: color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent);
  --mat-chip-elevated-selected-container-color: var(--mat-sys-secondary-container);
  --mat-chip-flat-disabled-selected-container-color: color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent);
  --mat-chip-focus-outline-color: var(--mat-sys-on-surface-variant);
  --mat-chip-focus-state-layer-color: var(--mat-sys-on-surface-variant);
  --mat-chip-focus-state-layer-opacity: 0.12;
  --mat-chip-hover-state-layer-color: var(--mat-sys-on-surface-variant);
  --mat-chip-hover-state-layer-opacity: 0.08;
  --mat-chip-label-text-color: var(--mat-sys-on-surface-variant);
  --mat-chip-outline-color: var(--mat-sys-outline);
  --mat-chip-selected-disabled-trailing-icon-color: var(--mat-sys-on-surface);
  --mat-chip-selected-focus-state-layer-color: var(--mat-sys-on-secondary-container);
  --mat-chip-selected-focus-state-layer-opacity: 0.12;
  --mat-chip-selected-hover-state-layer-color: var(--mat-sys-on-secondary-container);
  --mat-chip-selected-hover-state-layer-opacity: 0.08;
  --mat-chip-selected-label-text-color: var(--mat-sys-on-secondary-container);
  --mat-chip-selected-trailing-action-state-layer-color: var(--mat-sys-on-secondary-container);
  --mat-chip-selected-trailing-icon-color: var(--mat-sys-on-secondary-container);
  --mat-chip-trailing-action-focus-state-layer-opacity: 0.12;
  --mat-chip-trailing-action-hover-state-layer-opacity: 0.08;
  --mat-chip-trailing-action-state-layer-color: var(--mat-sys-on-surface-variant);
  --mat-chip-with-icon-disabled-icon-color: var(--mat-sys-on-surface);
  --mat-chip-with-icon-icon-color: var(--mat-sys-on-surface-variant);
  --mat-chip-with-icon-selected-icon-color: var(--mat-sys-on-secondary-container);
  --mat-chip-with-trailing-icon-disabled-trailing-icon-color: var(--mat-sys-on-surface);
  --mat-chip-with-trailing-icon-trailing-icon-color: var(--mat-sys-on-surface-variant);
  --mat-chip-container-height: 32px;
  --mat-chip-label-text-font: var(--mat-sys-label-large-font);
  --mat-chip-label-text-line-height: var(--mat-sys-label-large-line-height);
  --mat-chip-label-text-size: var(--mat-sys-label-large-size);
  --mat-chip-label-text-tracking: var(--mat-sys-label-large-tracking);
  --mat-chip-label-text-weight: var(--mat-sys-label-large-weight);
  --mat-slide-toggle-disabled-selected-handle-opacity: 1;
  --mat-slide-toggle-disabled-selected-icon-opacity: 0.38;
  --mat-slide-toggle-disabled-track-opacity: 0.12;
  --mat-slide-toggle-disabled-unselected-handle-opacity: 0.38;
  --mat-slide-toggle-disabled-unselected-icon-opacity: 0.38;
  --mat-slide-toggle-disabled-unselected-track-outline-width: 2px;
  --mat-slide-toggle-handle-shape: 9999px;
  --mat-slide-toggle-hidden-track-opacity: 0;
  --mat-slide-toggle-hidden-track-transition: opacity 75ms;
  --mat-slide-toggle-pressed-handle-size: 28px;
  --mat-slide-toggle-selected-handle-horizontal-margin: 0 24px;
  --mat-slide-toggle-selected-handle-size: 24px;
  --mat-slide-toggle-selected-icon-size: 16px;
  --mat-slide-toggle-selected-pressed-handle-horizontal-margin: 0 22px;
  --mat-slide-toggle-selected-track-outline-color: transparent;
  --mat-slide-toggle-selected-track-outline-width: 2px;
  --mat-slide-toggle-selected-with-icon-handle-horizontal-margin: 0 24px;
  --mat-slide-toggle-state-layer-size: 40px;
  --mat-slide-toggle-track-height: 32px;
  --mat-slide-toggle-track-outline-width: 2px;
  --mat-slide-toggle-track-shape: 9999px;
  --mat-slide-toggle-track-width: 52px;
  --mat-slide-toggle-unselected-handle-horizontal-margin: 0 8px;
  --mat-slide-toggle-unselected-handle-size: 16px;
  --mat-slide-toggle-unselected-icon-size: 16px;
  --mat-slide-toggle-unselected-pressed-handle-horizontal-margin: 0 2px;
  --mat-slide-toggle-unselected-with-icon-handle-horizontal-margin: 0 4px;
  --mat-slide-toggle-visible-track-opacity: 1;
  --mat-slide-toggle-visible-track-transition: opacity 75ms;
  --mat-slide-toggle-with-icon-handle-size: 24px;
  --mat-slide-toggle-disabled-selected-handle-color: var(--mat-sys-surface);
  --mat-slide-toggle-disabled-selected-icon-color: var(--mat-sys-on-surface);
  --mat-slide-toggle-disabled-selected-track-color: var(--mat-sys-on-surface);
  --mat-slide-toggle-disabled-unselected-handle-color: var(--mat-sys-on-surface);
  --mat-slide-toggle-disabled-unselected-icon-color: var(--mat-sys-surface-variant);
  --mat-slide-toggle-disabled-unselected-track-color: var(--mat-sys-surface-variant);
  --mat-slide-toggle-disabled-unselected-track-outline-color: var(--mat-sys-on-surface);
  --mat-slide-toggle-label-text-color: var(--mat-sys-on-surface);
  --mat-slide-toggle-selected-focus-handle-color: var(--mat-sys-primary-container);
  --mat-slide-toggle-selected-focus-state-layer-color: var(--mat-sys-primary);
  --mat-slide-toggle-selected-focus-state-layer-opacity: 0.12;
  --mat-slide-toggle-selected-focus-track-color: var(--mat-sys-primary);
  --mat-slide-toggle-selected-handle-color: var(--mat-sys-on-primary);
  --mat-slide-toggle-selected-hover-handle-color: var(--mat-sys-primary-container);
  --mat-slide-toggle-selected-hover-state-layer-color: var(--mat-sys-primary);
  --mat-slide-toggle-selected-hover-state-layer-opacity: 0.08;
  --mat-slide-toggle-selected-hover-track-color: var(--mat-sys-primary);
  --mat-slide-toggle-selected-icon-color: var(--mat-sys-on-primary-container);
  --mat-slide-toggle-selected-pressed-handle-color: var(--mat-sys-primary-container);
  --mat-slide-toggle-selected-pressed-state-layer-color: var(--mat-sys-primary);
  --mat-slide-toggle-selected-pressed-state-layer-opacity: 0.12;
  --mat-slide-toggle-selected-pressed-track-color: var(--mat-sys-primary);
  --mat-slide-toggle-selected-track-color: var(--mat-sys-primary);
  --mat-slide-toggle-track-outline-color: var(--mat-sys-outline);
  --mat-slide-toggle-unselected-focus-handle-color: var(--mat-sys-on-surface-variant);
  --mat-slide-toggle-unselected-focus-state-layer-color: var(--mat-sys-on-surface);
  --mat-slide-toggle-unselected-focus-state-layer-opacity: 0.12;
  --mat-slide-toggle-unselected-focus-track-color: var(--mat-sys-surface-variant);
  --mat-slide-toggle-unselected-handle-color: var(--mat-sys-outline);
  --mat-slide-toggle-unselected-hover-handle-color: var(--mat-sys-on-surface-variant);
  --mat-slide-toggle-unselected-hover-state-layer-color: var(--mat-sys-on-surface);
  --mat-slide-toggle-unselected-hover-state-layer-opacity: 0.08;
  --mat-slide-toggle-unselected-hover-track-color: var(--mat-sys-surface-variant);
  --mat-slide-toggle-unselected-icon-color: var(--mat-sys-surface-variant);
  --mat-slide-toggle-unselected-pressed-handle-color: var(--mat-sys-on-surface-variant);
  --mat-slide-toggle-unselected-pressed-state-layer-color: var(--mat-sys-on-surface);
  --mat-slide-toggle-unselected-pressed-state-layer-opacity: 0.12;
  --mat-slide-toggle-unselected-pressed-track-color: var(--mat-sys-surface-variant);
  --mat-slide-toggle-unselected-track-color: var(--mat-sys-surface-variant);
  --mat-slide-toggle-label-text-font: var(--mat-sys-body-medium-font);
  --mat-slide-toggle-label-text-line-height: var(--mat-sys-body-medium-line-height);
  --mat-slide-toggle-label-text-size: var(--mat-sys-body-medium-size);
  --mat-slide-toggle-label-text-tracking: var(--mat-sys-body-medium-tracking);
  --mat-slide-toggle-label-text-weight: var(--mat-sys-body-medium-weight);
  --mat-radio-disabled-unselected-icon-opacity: 0.38;
  --mat-radio-disabled-selected-icon-opacity: 0.38;
  --mat-radio-checked-ripple-color: var(--mat-sys-primary);
  --mat-radio-disabled-label-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-radio-disabled-selected-icon-color: var(--mat-sys-on-surface);
  --mat-radio-disabled-unselected-icon-color: var(--mat-sys-on-surface);
  --mat-radio-label-text-color: var(--mat-sys-on-surface);
  --mat-radio-ripple-color: var(--mat-sys-on-surface);
  --mat-radio-selected-focus-icon-color: var(--mat-sys-primary);
  --mat-radio-selected-hover-icon-color: var(--mat-sys-primary);
  --mat-radio-selected-icon-color: var(--mat-sys-primary);
  --mat-radio-selected-pressed-icon-color: var(--mat-sys-primary);
  --mat-radio-unselected-focus-icon-color: var(--mat-sys-on-surface);
  --mat-radio-unselected-hover-icon-color: var(--mat-sys-on-surface);
  --mat-radio-unselected-icon-color: var(--mat-sys-on-surface-variant);
  --mat-radio-unselected-pressed-icon-color: var(--mat-sys-on-surface);
  --mat-radio-touch-target-display: block;
  --mat-radio-state-layer-size: 40px;
  --mat-radio-label-text-font: var(--mat-sys-body-medium-font);
  --mat-radio-label-text-line-height: var(--mat-sys-body-medium-line-height);
  --mat-radio-label-text-size: var(--mat-sys-body-medium-size);
  --mat-radio-label-text-tracking: var(--mat-sys-body-medium-tracking);
  --mat-radio-label-text-weight: var(--mat-sys-body-medium-weight);
  --mat-slider-value-indicator-opacity: 1;
  --mat-slider-value-indicator-padding: 0;
  --mat-slider-value-indicator-width: 28px;
  --mat-slider-value-indicator-height: 28px;
  --mat-slider-value-indicator-caret-display: none;
  --mat-slider-value-indicator-border-radius: 50% 50% 50% 0;
  --mat-slider-value-indicator-text-transform: rotate(45deg);
  --mat-slider-value-indicator-container-transform: translateX(-50%) rotate(-45deg);
  --mat-slider-active-track-height: 4px;
  --mat-slider-handle-height: 20px;
  --mat-slider-handle-width: 20px;
  --mat-slider-inactive-track-height: 4px;
  --mat-slider-with-overlap-handle-outline-width: 1px;
  --mat-slider-with-tick-marks-active-container-opacity: 0.38;
  --mat-slider-with-tick-marks-container-size: 2px;
  --mat-slider-with-tick-marks-inactive-container-opacity: 0.38;
  --mat-slider-active-track-color: var(--mat-sys-primary);
  --mat-slider-active-track-shape: 9999px;
  --mat-slider-disabled-active-track-color: var(--mat-sys-on-surface);
  --mat-slider-disabled-handle-color: var(--mat-sys-on-surface);
  --mat-slider-disabled-inactive-track-color: var(--mat-sys-on-surface);
  --mat-slider-focus-handle-color: var(--mat-sys-primary);
  --mat-slider-focus-state-layer-color: color-mix(in srgb, var(--mat-sys-primary) 20%, transparent);
  --mat-slider-handle-color: var(--mat-sys-primary);
  --mat-slider-handle-elevation: 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);
  --mat-slider-handle-shape: 9999px;
  --mat-slider-hover-handle-color: var(--mat-sys-primary);
  --mat-slider-hover-state-layer-color: color-mix(in srgb, var(--mat-sys-primary) 5%, transparent);
  --mat-slider-inactive-track-color: var(--mat-sys-surface-variant);
  --mat-slider-inactive-track-shape: 9999px;
  --mat-slider-label-container-color: var(--mat-sys-primary);
  --mat-slider-label-label-text-color: var(--mat-sys-on-primary);
  --mat-slider-ripple-color: var(--mat-sys-primary);
  --mat-slider-with-overlap-handle-outline-color: var(--mat-sys-on-primary);
  --mat-slider-with-tick-marks-active-container-color: var(--mat-sys-on-primary);
  --mat-slider-with-tick-marks-container-shape: 9999px;
  --mat-slider-with-tick-marks-disabled-container-color: var(--mat-sys-on-surface);
  --mat-slider-with-tick-marks-inactive-container-color: var(--mat-sys-on-surface-variant);
  --mat-slider-label-label-text-font: var(--mat-sys-label-medium-font);
  --mat-slider-label-label-text-line-height: var(--mat-sys-label-medium-line-height);
  --mat-slider-label-label-text-size: var(--mat-sys-label-medium-size);
  --mat-slider-label-label-text-tracking: var(--mat-sys-label-medium-tracking);
  --mat-slider-label-label-text-weight: var(--mat-sys-label-medium-weight);
  --mat-menu-divider-bottom-spacing: 8px;
  --mat-menu-divider-top-spacing: 8px;
  --mat-menu-item-icon-size: 24px;
  --mat-menu-item-spacing: 12px;
  --mat-menu-item-leading-spacing: 12px;
  --mat-menu-item-trailing-spacing: 12px;
  --mat-menu-item-with-icon-leading-spacing: 12px;
  --mat-menu-item-with-icon-trailing-spacing: 12px;
  --mat-menu-container-shape: 4px;
  --mat-menu-divider-color: var(--mat-sys-surface-variant);
  --mat-menu-item-label-text-color: var(--mat-sys-on-surface);
  --mat-menu-item-icon-color: var(--mat-sys-on-surface-variant);
  --mat-menu-item-hover-state-layer-color: color-mix(in srgb, var(--mat-sys-on-surface) 8%, transparent);
  --mat-menu-item-focus-state-layer-color: color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent);
  --mat-menu-container-color: var(--mat-sys-surface-container);
  --mat-menu-container-elevation-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
  --mat-menu-item-label-text-font: var(--mat-sys-label-large-font);
  --mat-menu-item-label-text-line-height: var(--mat-sys-label-large-line-height);
  --mat-menu-item-label-text-size: var(--mat-sys-label-large-size);
  --mat-menu-item-label-text-tracking: var(--mat-sys-label-large-tracking);
  --mat-menu-item-label-text-weight: var(--mat-sys-label-large-weight);
  --mat-list-active-indicator-color: var(--mat-sys-secondary-container);
  --mat-list-active-indicator-shape: 9999px;
  --mat-list-list-item-container-color: transparent;
  --mat-list-list-item-container-shape: 0;
  --mat-list-list-item-disabled-label-text-color: var(--mat-sys-on-surface);
  --mat-list-list-item-disabled-label-text-opacity: 0.3;
  --mat-list-list-item-disabled-leading-icon-color: var(--mat-sys-on-surface);
  --mat-list-list-item-disabled-leading-icon-opacity: 0.38;
  --mat-list-list-item-disabled-state-layer-color: var(--mat-sys-on-surface);
  --mat-list-list-item-disabled-state-layer-opacity: 0.12;
  --mat-list-list-item-disabled-trailing-icon-color: var(--mat-sys-on-surface);
  --mat-list-list-item-disabled-trailing-icon-opacity: 0.38;
  --mat-list-list-item-focus-label-text-color: var(--mat-sys-on-surface);
  --mat-list-list-item-focus-state-layer-color: var(--mat-sys-on-surface);
  --mat-list-list-item-focus-state-layer-opacity: 0.12;
  --mat-list-list-item-hover-label-text-color: var(--mat-sys-on-surface);
  --mat-list-list-item-hover-state-layer-color: var(--mat-sys-on-surface);
  --mat-list-list-item-hover-state-layer-opacity: 0.08;
  --mat-list-list-item-label-text-color: var(--mat-sys-on-surface);
  --mat-list-list-item-leading-avatar-color: var(--mat-sys-primary-container);
  --mat-list-list-item-leading-avatar-shape: 9999px;
  --mat-list-list-item-leading-avatar-size: 40px;
  --mat-list-list-item-leading-icon-color: var(--mat-sys-on-surface-variant);
  --mat-list-list-item-leading-icon-size: 24px;
  --mat-list-list-item-selected-trailing-icon-color: var(--mat-sys-primary);
  --mat-list-list-item-supporting-text-color: var(--mat-sys-on-surface-variant);
  --mat-list-list-item-trailing-icon-color: var(--mat-sys-on-surface-variant);
  --mat-list-list-item-trailing-icon-size: 24px;
  --mat-list-list-item-trailing-supporting-text-color: var(--mat-sys-on-surface-variant);
  --mat-list-list-item-leading-icon-start-space: 16px;
  --mat-list-list-item-leading-icon-end-space: 16px;
  --mat-list-list-item-one-line-container-height: 48px;
  --mat-list-list-item-two-line-container-height: 64px;
  --mat-list-list-item-three-line-container-height: 88px;
  --mat-list-list-item-label-text-font: var(--mat-sys-body-large-font);
  --mat-list-list-item-label-text-line-height: var(--mat-sys-body-large-line-height);
  --mat-list-list-item-label-text-size: var(--mat-sys-body-large-size);
  --mat-list-list-item-label-text-tracking: var(--mat-sys-body-large-tracking);
  --mat-list-list-item-label-text-weight: var(--mat-sys-body-large-weight);
  --mat-list-list-item-supporting-text-font: var(--mat-sys-body-medium-font);
  --mat-list-list-item-supporting-text-line-height: var(--mat-sys-body-medium-line-height);
  --mat-list-list-item-supporting-text-size: var(--mat-sys-body-medium-size);
  --mat-list-list-item-supporting-text-tracking: var(--mat-sys-body-medium-tracking);
  --mat-list-list-item-supporting-text-weight: var(--mat-sys-body-medium-weight);
  --mat-list-list-item-trailing-supporting-text-font: var(--mat-sys-label-small-font);
  --mat-list-list-item-trailing-supporting-text-line-height: var(--mat-sys-label-small-line-height);
  --mat-list-list-item-trailing-supporting-text-size: var(--mat-sys-label-small-size);
  --mat-list-list-item-trailing-supporting-text-tracking: var(--mat-sys-label-small-tracking);
  --mat-list-list-item-trailing-supporting-text-weight: var(--mat-sys-label-small-weight);
  --mat-paginator-container-text-color: var(--mat-sys-on-surface);
  --mat-paginator-container-background-color: var(--mat-sys-surface);
  --mat-paginator-disabled-icon-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-paginator-enabled-icon-color: var(--mat-sys-on-surface-variant);
  --mat-paginator-container-size: 56px;
  --mat-paginator-form-field-container-height: 40px;
  --mat-paginator-form-field-container-vertical-padding: 8px;
  --mat-paginator-touch-target-display: block;
  --mat-paginator-container-text-font: var(--mat-sys-body-small-font);
  --mat-paginator-container-text-line-height: var(--mat-sys-body-small-line-height);
  --mat-paginator-container-text-size: var(--mat-sys-body-small-size);
  --mat-paginator-container-text-tracking: var(--mat-sys-body-small-tracking);
  --mat-paginator-container-text-weight: var(--mat-sys-body-small-weight);
  --mat-paginator-select-trigger-text-size: var(--mat-sys-body-small-size);
  --mat-tab-active-indicator-height: 2px;
  --mat-tab-active-indicator-shape: 0;
  --mat-tab-divider-height: 1px;
  --mat-tab-active-focus-indicator-color: var(--mat-sys-primary);
  --mat-tab-active-focus-label-text-color: var(--mat-sys-on-surface);
  --mat-tab-active-hover-indicator-color: var(--mat-sys-primary);
  --mat-tab-active-hover-label-text-color: var(--mat-sys-on-surface);
  --mat-tab-active-indicator-color: var(--mat-sys-primary);
  --mat-tab-active-label-text-color: var(--mat-sys-on-surface);
  --mat-tab-active-ripple-color: var(--mat-sys-on-surface);
  --mat-tab-divider-color: var(--mat-sys-surface-variant);
  --mat-tab-inactive-focus-label-text-color: var(--mat-sys-on-surface);
  --mat-tab-inactive-hover-label-text-color: var(--mat-sys-on-surface);
  --mat-tab-inactive-label-text-color: var(--mat-sys-on-surface);
  --mat-tab-inactive-ripple-color: var(--mat-sys-on-surface);
  --mat-tab-pagination-icon-color: var(--mat-sys-on-surface);
  --mat-tab-disabled-ripple-color: var(--mat-sys-on-surface-variant);
  --mat-tab-container-height: 48px;
  --mat-tab-label-text-font: var(--mat-sys-title-small-font);
  --mat-tab-label-text-line-height: var(--mat-sys-title-small-line-height);
  --mat-tab-label-text-size: var(--mat-sys-title-small-size);
  --mat-tab-label-text-tracking: var(--mat-sys-title-small-tracking);
  --mat-tab-label-text-weight: var(--mat-sys-title-small-weight);
  --mat-checkbox-selected-focus-state-layer-opacity: 0.12;
  --mat-checkbox-selected-hover-state-layer-opacity: 0.08;
  --mat-checkbox-selected-pressed-state-layer-opacity: 0.12;
  --mat-checkbox-unselected-focus-state-layer-opacity: 0.12;
  --mat-checkbox-unselected-hover-state-layer-opacity: 0.08;
  --mat-checkbox-unselected-pressed-state-layer-opacity: 0.12;
  --mat-checkbox-disabled-label-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-checkbox-disabled-selected-checkmark-color: var(--mat-sys-surface);
  --mat-checkbox-disabled-selected-icon-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-checkbox-disabled-unselected-icon-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-checkbox-label-text-color: var(--mat-sys-on-surface);
  --mat-checkbox-selected-checkmark-color: var(--mat-sys-on-primary);
  --mat-checkbox-selected-focus-icon-color: var(--mat-sys-primary);
  --mat-checkbox-selected-focus-state-layer-color: var(--mat-sys-primary);
  --mat-checkbox-selected-hover-icon-color: var(--mat-sys-primary);
  --mat-checkbox-selected-hover-state-layer-color: var(--mat-sys-primary);
  --mat-checkbox-selected-icon-color: var(--mat-sys-primary);
  --mat-checkbox-selected-pressed-icon-color: var(--mat-sys-primary);
  --mat-checkbox-selected-pressed-state-layer-color: var(--mat-sys-on-surface);
  --mat-checkbox-unselected-focus-icon-color: var(--mat-sys-on-surface);
  --mat-checkbox-unselected-focus-state-layer-color: var(--mat-sys-on-surface);
  --mat-checkbox-unselected-hover-icon-color: var(--mat-sys-on-surface);
  --mat-checkbox-unselected-hover-state-layer-color: var(--mat-sys-on-surface);
  --mat-checkbox-unselected-icon-color: var(--mat-sys-on-surface-variant);
  --mat-checkbox-unselected-pressed-state-layer-color: var(--mat-sys-primary);
  --mat-checkbox-touch-target-display: block;
  --mat-checkbox-state-layer-size: 40px;
  --mat-checkbox-label-text-font: var(--mat-sys-body-medium-font);
  --mat-checkbox-label-text-line-height: var(--mat-sys-body-medium-line-height);
  --mat-checkbox-label-text-size: var(--mat-sys-body-medium-size);
  --mat-checkbox-label-text-tracking: var(--mat-sys-body-medium-tracking);
  --mat-checkbox-label-text-weight: var(--mat-sys-body-medium-weight);
  --mat-button-filled-container-shape: 9999px;
  --mat-button-filled-horizontal-padding: 24px;
  --mat-button-filled-icon-offset: -8px;
  --mat-button-filled-icon-spacing: 8px;
  --mat-button-outlined-container-shape: 9999px;
  --mat-button-outlined-horizontal-padding: 24px;
  --mat-button-outlined-icon-offset: -8px;
  --mat-button-outlined-icon-spacing: 8px;
  --mat-button-outlined-outline-width: 1px;
  --mat-button-protected-container-shape: 9999px;
  --mat-button-protected-horizontal-padding: 24px;
  --mat-button-protected-icon-offset: -8px;
  --mat-button-protected-icon-spacing: 8px;
  --mat-button-text-container-shape: 9999px;
  --mat-button-text-horizontal-padding: 12px;
  --mat-button-text-icon-offset: -4px;
  --mat-button-text-icon-spacing: 8px;
  --mat-button-text-with-icon-horizontal-padding: 16px;
  --mat-button-tonal-container-shape: 9999px;
  --mat-button-tonal-horizontal-padding: 24px;
  --mat-button-tonal-icon-offset: -8px;
  --mat-button-tonal-icon-spacing: 8px;
  --mat-button-filled-container-color: var(--mat-sys-primary);
  --mat-button-filled-disabled-container-color: color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent);
  --mat-button-filled-disabled-label-text-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-button-filled-disabled-state-layer-color: var(--mat-sys-on-surface-variant);
  --mat-button-filled-focus-state-layer-opacity: 0.12;
  --mat-button-filled-hover-state-layer-opacity: 0.08;
  --mat-button-filled-label-text-color: var(--mat-sys-on-primary);
  --mat-button-filled-pressed-state-layer-opacity: 0.12;
  --mat-button-filled-ripple-color: color-mix(in srgb, var(--mat-sys-on-primary) 12%, transparent);
  --mat-button-filled-state-layer-color: var(--mat-sys-on-primary);
  --mat-button-outlined-disabled-label-text-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-button-outlined-disabled-outline-color: color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent);
  --mat-button-outlined-disabled-state-layer-color: var(--mat-sys-on-surface-variant);
  --mat-button-outlined-focus-state-layer-opacity: 0.12;
  --mat-button-outlined-hover-state-layer-opacity: 0.08;
  --mat-button-outlined-label-text-color: var(--mat-sys-primary);
  --mat-button-outlined-outline-color: var(--mat-sys-outline);
  --mat-button-outlined-pressed-state-layer-opacity: 0.12;
  --mat-button-outlined-ripple-color: color-mix(in srgb, var(--mat-sys-primary) 12%, transparent);
  --mat-button-outlined-state-layer-color: var(--mat-sys-primary);
  --mat-button-protected-container-color: var(--mat-sys-surface);
  --mat-button-protected-container-elevation-shadow: 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);
  --mat-button-protected-disabled-container-color: color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent);
  --mat-button-protected-disabled-container-elevation-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);
  --mat-button-protected-disabled-label-text-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-button-protected-disabled-state-layer-color: var(--mat-sys-on-surface-variant);
  --mat-button-protected-focus-container-elevation-shadow: 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);
  --mat-button-protected-focus-state-layer-opacity: 0.12;
  --mat-button-protected-hover-container-elevation-shadow: 0px 3px 3px -2px rgba(0, 0, 0, 0.2), 0px 3px 4px 0px rgba(0, 0, 0, 0.14), 0px 1px 8px 0px rgba(0, 0, 0, 0.12);
  --mat-button-protected-hover-state-layer-opacity: 0.08;
  --mat-button-protected-label-text-color: var(--mat-sys-primary);
  --mat-button-protected-pressed-container-elevation-shadow: 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);
  --mat-button-protected-pressed-state-layer-opacity: 0.12;
  --mat-button-protected-ripple-color: color-mix(in srgb, var(--mat-sys-primary) 12%, transparent);
  --mat-button-protected-state-layer-color: var(--mat-sys-primary);
  --mat-button-text-disabled-label-text-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-button-text-disabled-state-layer-color: var(--mat-sys-on-surface-variant);
  --mat-button-text-focus-state-layer-opacity: 0.12;
  --mat-button-text-hover-state-layer-opacity: 0.08;
  --mat-button-text-label-text-color: var(--mat-sys-primary);
  --mat-button-text-pressed-state-layer-opacity: 0.12;
  --mat-button-text-ripple-color: color-mix(in srgb, var(--mat-sys-primary) 12%, transparent);
  --mat-button-text-state-layer-color: var(--mat-sys-primary);
  --mat-button-tonal-container-color: var(--mat-sys-secondary-container);
  --mat-button-tonal-disabled-container-color: color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent);
  --mat-button-tonal-disabled-label-text-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-button-tonal-disabled-state-layer-color: var(--mat-sys-on-surface-variant);
  --mat-button-tonal-focus-state-layer-opacity: 0.12;
  --mat-button-tonal-hover-state-layer-opacity: 0.08;
  --mat-button-tonal-label-text-color: var(--mat-sys-on-secondary-container);
  --mat-button-tonal-pressed-state-layer-opacity: 0.12;
  --mat-button-tonal-ripple-color: color-mix(in srgb, var(--mat-sys-on-secondary-container) 12%, transparent);
  --mat-button-tonal-state-layer-color: var(--mat-sys-on-secondary-container);
  --mat-button-filled-touch-target-display: block;
  --mat-button-filled-container-height: 40px;
  --mat-button-outlined-container-height: 40px;
  --mat-button-outlined-touch-target-display: block;
  --mat-button-protected-touch-target-display: block;
  --mat-button-protected-container-height: 40px;
  --mat-button-text-touch-target-display: block;
  --mat-button-text-container-height: 40px;
  --mat-button-tonal-container-height: 40px;
  --mat-button-tonal-touch-target-display: block;
  --mat-button-filled-label-text-font: var(--mat-sys-label-large-font);
  --mat-button-filled-label-text-size: var(--mat-sys-label-large-size);
  --mat-button-filled-label-text-tracking: var(--mat-sys-label-large-tracking);
  --mat-button-filled-label-text-weight: var(--mat-sys-label-large-weight);
  --mat-button-outlined-label-text-font: var(--mat-sys-label-large-font);
  --mat-button-outlined-label-text-size: var(--mat-sys-label-large-size);
  --mat-button-outlined-label-text-tracking: var(--mat-sys-label-large-tracking);
  --mat-button-outlined-label-text-weight: var(--mat-sys-label-large-weight);
  --mat-button-protected-label-text-font: var(--mat-sys-label-large-font);
  --mat-button-protected-label-text-size: var(--mat-sys-label-large-size);
  --mat-button-protected-label-text-tracking: var(--mat-sys-label-large-tracking);
  --mat-button-protected-label-text-weight: var(--mat-sys-label-large-weight);
  --mat-button-text-label-text-font: var(--mat-sys-label-large-font);
  --mat-button-text-label-text-size: var(--mat-sys-label-large-size);
  --mat-button-text-label-text-tracking: var(--mat-sys-label-large-tracking);
  --mat-button-text-label-text-weight: var(--mat-sys-label-large-weight);
  --mat-button-tonal-label-text-font: var(--mat-sys-label-large-font);
  --mat-button-tonal-label-text-size: var(--mat-sys-label-large-size);
  --mat-button-tonal-label-text-tracking: var(--mat-sys-label-large-tracking);
  --mat-button-tonal-label-text-weight: var(--mat-sys-label-large-weight);
  --mat-icon-button-icon-size: 24px;
  --mat-icon-button-container-shape: 9999px;
  --mat-icon-button-disabled-icon-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-icon-button-disabled-state-layer-color: var(--mat-sys-on-surface-variant);
  --mat-icon-button-focus-state-layer-opacity: 0.12;
  --mat-icon-button-hover-state-layer-opacity: 0.08;
  --mat-icon-button-icon-color: var(--mat-sys-on-surface-variant);
  --mat-icon-button-pressed-state-layer-opacity: 0.12;
  --mat-icon-button-ripple-color: color-mix(in srgb, var(--mat-sys-on-surface-variant) 12%, transparent);
  --mat-icon-button-state-layer-color: var(--mat-sys-on-surface-variant);
  --mat-icon-button-touch-target-display: block;
  --mat-icon-button-state-layer-size: 40px;
  --mat-fab-container-shape: 16px;
  --mat-fab-extended-container-height: 56px;
  --mat-fab-extended-container-shape: 16px;
  --mat-fab-small-container-shape: 12px;
  --mat-fab-container-color: var(--mat-sys-primary-container);
  --mat-fab-container-elevation-shadow: 0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);
  --mat-fab-disabled-state-container-color: color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent);
  --mat-fab-disabled-state-foreground-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-fab-extended-container-elevation-shadow: 0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);
  --mat-fab-extended-focus-container-elevation-shadow: 0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);
  --mat-fab-extended-hover-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);
  --mat-fab-extended-pressed-container-elevation-shadow: 6;
  --mat-fab-focus-container-elevation-shadow: 0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);
  --mat-fab-focus-state-layer-opacity: 0.12;
  --mat-fab-foreground-color: var(--mat-sys-on-primary-container);
  --mat-fab-hover-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);
  --mat-fab-hover-state-layer-opacity: 0.08;
  --mat-fab-pressed-container-elevation-shadow: 0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);
  --mat-fab-pressed-state-layer-opacity: 0.12;
  --mat-fab-ripple-color: color-mix(in srgb, var(--mat-sys-on-primary-container) 12%, transparent);
  --mat-fab-small-container-color: var(--mat-sys-primary-container);
  --mat-fab-small-container-elevation-shadow: 0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);
  --mat-fab-small-disabled-state-container-color: color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent);
  --mat-fab-small-disabled-state-foreground-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-fab-small-focus-container-elevation-shadow: 0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);
  --mat-fab-small-focus-state-layer-opacity: 0.12;
  --mat-fab-small-foreground-color: var(--mat-sys-on-primary-container);
  --mat-fab-small-hover-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);
  --mat-fab-small-hover-state-layer-opacity: 0.08;
  --mat-fab-small-pressed-container-elevation-shadow: 0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);
  --mat-fab-small-pressed-state-layer-opacity: 0.12;
  --mat-fab-small-ripple-color: color-mix(in srgb, var(--mat-sys-on-primary-container) 12%, transparent);
  --mat-fab-small-state-layer-color: var(--mat-sys-on-primary-container);
  --mat-fab-state-layer-color: var(--mat-sys-on-primary-container);
  --mat-fab-touch-target-display: block;
  --mat-fab-extended-label-text-font: var(--mat-sys-label-large-font);
  --mat-fab-extended-label-text-size: var(--mat-sys-label-large-size);
  --mat-fab-extended-label-text-tracking: var(--mat-sys-label-large-tracking);
  --mat-fab-extended-label-text-weight: var(--mat-sys-label-large-weight);
  --mat-snack-bar-container-shape: 4px;
  --mat-snack-bar-button-color: var(--mat-sys-inverse-primary);
  --mat-snack-bar-container-color: var(--mat-sys-inverse-surface);
  --mat-snack-bar-supporting-text-color: var(--mat-sys-inverse-on-surface);
  --mat-snack-bar-supporting-text-font: var(--mat-sys-body-medium-font);
  --mat-snack-bar-supporting-text-line-height: var(--mat-sys-body-medium-line-height);
  --mat-snack-bar-supporting-text-size: var(--mat-sys-body-medium-size);
  --mat-snack-bar-supporting-text-weight: var(--mat-sys-body-medium-weight);
  --mat-table-row-item-outline-width: 1px;
  --mat-table-background-color: var(--mat-sys-surface);
  --mat-table-header-headline-color: var(--mat-sys-on-surface);
  --mat-table-row-item-label-text-color: var(--mat-sys-on-surface);
  --mat-table-row-item-outline-color: var(--mat-sys-outline);
  --mat-table-header-container-height: 56px;
  --mat-table-footer-container-height: 52px;
  --mat-table-row-item-container-height: 52px;
  --mat-table-header-headline-font: var(--mat-sys-title-small-font);
  --mat-table-header-headline-line-height: var(--mat-sys-title-small-line-height);
  --mat-table-header-headline-size: var(--mat-sys-title-small-size);
  --mat-table-header-headline-tracking: var(--mat-sys-title-small-tracking);
  --mat-table-header-headline-weight: var(--mat-sys-title-small-weight);
  --mat-table-row-item-label-text-font: var(--mat-sys-body-medium-font);
  --mat-table-row-item-label-text-line-height: var(--mat-sys-body-medium-line-height);
  --mat-table-row-item-label-text-size: var(--mat-sys-body-medium-size);
  --mat-table-row-item-label-text-tracking: var(--mat-sys-body-medium-tracking);
  --mat-table-row-item-label-text-weight: var(--mat-sys-body-medium-weight);
  --mat-table-footer-supporting-text-font: var(--mat-sys-body-medium-font);
  --mat-table-footer-supporting-text-line-height: var(--mat-sys-body-medium-line-height);
  --mat-table-footer-supporting-text-size: var(--mat-sys-body-medium-size);
  --mat-table-footer-supporting-text-tracking: var(--mat-sys-body-medium-tracking);
  --mat-table-footer-supporting-text-weight: var(--mat-sys-body-medium-weight);
  --mat-progress-spinner-size: 48px;
  --mat-progress-spinner-active-indicator-width: 4px;
  --mat-progress-spinner-active-indicator-color: var(--mat-sys-primary);
  --mat-badge-container-offset: -12px 0;
  --mat-badge-container-overlap-offset: -12px;
  --mat-badge-container-padding: 0 4px;
  --mat-badge-container-shape: 9999px;
  --mat-badge-container-size: 16px;
  --mat-badge-large-size-container-offset: -12px 0;
  --mat-badge-large-size-container-overlap-offset: -12px;
  --mat-badge-large-size-container-padding: 0 4px;
  --mat-badge-large-size-container-size: 16px;
  --mat-badge-legacy-container-size: unset;
  --mat-badge-legacy-large-size-container-size: unset;
  --mat-badge-legacy-small-size-container-size: unset;
  --mat-badge-small-size-container-offset: -6px 0;
  --mat-badge-small-size-container-overlap-offset: -6px;
  --mat-badge-small-size-container-padding: 0;
  --mat-badge-small-size-container-size: 6px;
  --mat-badge-background-color: var(--mat-sys-error);
  --mat-badge-disabled-state-background-color: color-mix(in srgb, var(--mat-sys-error) 38%, transparent);
  --mat-badge-disabled-state-text-color: var(--mat-sys-on-error);
  --mat-badge-text-color: var(--mat-sys-on-error);
  --mat-badge-large-size-line-height: 16px;
  --mat-badge-large-size-text-size: var(--mat-sys-label-small-size);
  --mat-badge-line-height: 16px;
  --mat-badge-small-size-line-height: 6px;
  --mat-badge-small-size-text-size: 0;
  --mat-badge-text-font: var(--mat-sys-label-small-font);
  --mat-badge-text-size: var(--mat-sys-label-small-size);
  --mat-badge-text-weight: var(--mat-sys-label-small-weight);
  --mat-bottom-sheet-container-shape: 28px;
  --mat-bottom-sheet-container-text-color: var(--mat-sys-on-surface);
  --mat-bottom-sheet-container-background-color: var(--mat-sys-surface-container-low);
  --mat-bottom-sheet-container-text-font: var(--mat-sys-body-large-font);
  --mat-bottom-sheet-container-text-line-height: var(--mat-sys-body-large-line-height);
  --mat-bottom-sheet-container-text-size: var(--mat-sys-body-large-size);
  --mat-bottom-sheet-container-text-tracking: var(--mat-sys-body-large-tracking);
  --mat-bottom-sheet-container-text-weight: var(--mat-sys-body-large-weight);
  --mat-button-toggle-focus-state-layer-opacity: 0.12;
  --mat-button-toggle-hover-state-layer-opacity: 0.08;
  --mat-button-toggle-shape: 28px;
  --mat-button-toggle-background-color: transparent;
  --mat-button-toggle-disabled-selected-state-background-color: color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent);
  --mat-button-toggle-disabled-selected-state-text-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-button-toggle-disabled-state-background-color: transparent;
  --mat-button-toggle-disabled-state-text-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-button-toggle-divider-color: var(--mat-sys-outline);
  --mat-button-toggle-selected-state-background-color: var(--mat-sys-secondary-container);
  --mat-button-toggle-selected-state-text-color: var(--mat-sys-on-secondary-container);
  --mat-button-toggle-state-layer-color: var(--mat-sys-on-surface);
  --mat-button-toggle-text-color: var(--mat-sys-on-surface);
  --mat-button-toggle-height: 40px;
  --mat-button-toggle-label-text-font: var(--mat-sys-label-large-font);
  --mat-button-toggle-label-text-line-height: var(--mat-sys-label-large-line-height);
  --mat-button-toggle-label-text-size: var(--mat-sys-label-large-size);
  --mat-button-toggle-label-text-tracking: var(--mat-sys-label-large-tracking);
  --mat-button-toggle-label-text-weight: var(--mat-sys-label-large-weight);
  --mat-datepicker-calendar-container-elevation-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);
  --mat-datepicker-calendar-container-shape: 16px;
  --mat-datepicker-calendar-container-touch-elevation-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);
  --mat-datepicker-calendar-container-touch-shape: 28px;
  --mat-datepicker-calendar-body-label-text-color: var(--mat-sys-on-surface);
  --mat-datepicker-calendar-container-background-color: var(--mat-sys-surface-container-high);
  --mat-datepicker-calendar-container-text-color: var(--mat-sys-on-surface);
  --mat-datepicker-calendar-date-disabled-state-text-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-datepicker-calendar-date-focus-state-background-color: color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent);
  --mat-datepicker-calendar-date-hover-state-background-color: color-mix(in srgb, var(--mat-sys-on-surface) 8%, transparent);
  --mat-datepicker-calendar-date-in-comparison-range-state-background-color: var(--mat-sys-tertiary-container);
  --mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color: var(--mat-sys-secondary);
  --mat-datepicker-calendar-date-in-overlap-range-state-background-color: var(--mat-sys-secondary-container);
  --mat-datepicker-calendar-date-in-range-state-background-color: var(--mat-sys-primary-container);
  --mat-datepicker-calendar-date-outline-color: transparent;
  --mat-datepicker-calendar-date-preview-state-outline-color: var(--mat-sys-primary);
  --mat-datepicker-calendar-date-selected-disabled-state-background-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-datepicker-calendar-date-selected-state-background-color: var(--mat-sys-primary);
  --mat-datepicker-calendar-date-selected-state-text-color: var(--mat-sys-on-primary);
  --mat-datepicker-calendar-date-text-color: var(--mat-sys-on-surface);
  --mat-datepicker-calendar-date-today-disabled-state-outline-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-datepicker-calendar-date-today-outline-color: var(--mat-sys-primary);
  --mat-datepicker-calendar-date-today-selected-state-outline-color: var(--mat-sys-primary);
  --mat-datepicker-calendar-header-divider-color: transparent;
  --mat-datepicker-calendar-header-text-color: var(--mat-sys-on-surface-variant);
  --mat-datepicker-calendar-navigation-button-icon-color: var(--mat-sys-on-surface-variant);
  --mat-datepicker-calendar-period-button-icon-color: var(--mat-sys-on-surface-variant);
  --mat-datepicker-calendar-period-button-text-color: var(--mat-sys-on-surface-variant);
  --mat-datepicker-range-input-disabled-state-separator-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-datepicker-range-input-disabled-state-text-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-datepicker-range-input-separator-color: var(--mat-sys-on-surface);
  --mat-datepicker-toggle-active-state-icon-color: var(--mat-sys-on-surface-variant);
  --mat-datepicker-toggle-icon-color: var(--mat-sys-on-surface-variant);
  --mat-datepicker-calendar-body-label-text-size: var(--mat-sys-title-small-size);
  --mat-datepicker-calendar-body-label-text-weight: var(--mat-sys-title-small-weight);
  --mat-datepicker-calendar-header-text-size: var(--mat-sys-title-small-size);
  --mat-datepicker-calendar-header-text-weight: var(--mat-sys-title-small-weight);
  --mat-datepicker-calendar-period-button-text-size: var(--mat-sys-title-small-size);
  --mat-datepicker-calendar-period-button-text-weight: var(--mat-sys-title-small-weight);
  --mat-datepicker-calendar-text-font: var(--mat-sys-body-medium-font);
  --mat-datepicker-calendar-text-size: var(--mat-sys-body-medium-size);
  --mat-divider-width: 1px;
  --mat-divider-color: var(--mat-sys-outline);
  --mat-expansion-container-shape: 12px;
  --mat-expansion-header-indicator-display: inline-block;
  --mat-expansion-legacy-header-indicator-display: none;
  --mat-expansion-actions-divider-color: var(--mat-sys-outline);
  --mat-expansion-container-background-color: var(--mat-sys-surface);
  --mat-expansion-container-text-color: var(--mat-sys-on-surface);
  --mat-expansion-header-description-color: var(--mat-sys-on-surface-variant);
  --mat-expansion-header-disabled-state-text-color: color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent);
  --mat-expansion-header-focus-state-layer-color: color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent);
  --mat-expansion-header-hover-state-layer-color: color-mix(in srgb, var(--mat-sys-on-surface) 8%, transparent);
  --mat-expansion-header-indicator-color: var(--mat-sys-on-surface-variant);
  --mat-expansion-header-text-color: var(--mat-sys-on-surface);
  --mat-expansion-header-collapsed-state-height: 48px;
  --mat-expansion-header-expanded-state-height: 64px;
  --mat-expansion-container-text-font: var(--mat-sys-body-large-font);
  --mat-expansion-container-text-line-height: var(--mat-sys-body-large-line-height);
  --mat-expansion-container-text-size: var(--mat-sys-body-large-size);
  --mat-expansion-container-text-tracking: var(--mat-sys-body-large-tracking);
  --mat-expansion-container-text-weight: var(--mat-sys-body-large-weight);
  --mat-expansion-header-text-font: var(--mat-sys-title-medium-font);
  --mat-expansion-header-text-line-height: var(--mat-sys-title-medium-line-height);
  --mat-expansion-header-text-size: var(--mat-sys-title-medium-size);
  --mat-expansion-header-text-tracking: var(--mat-sys-title-medium-tracking);
  --mat-expansion-header-text-weight: var(--mat-sys-title-medium-weight);
  --mat-grid-list-tile-header-primary-text-size: var(--mat-sys-body-large);
  --mat-grid-list-tile-header-secondary-text-size: var(--mat-sys-body-medium);
  --mat-grid-list-tile-footer-primary-text-size: var(--mat-sys-body-large);
  --mat-grid-list-tile-footer-secondary-text-size: var(--mat-sys-body-medium);
  --mat-icon-color: inherit;
  --mat-sidenav-container-shape: 16px;
  --mat-sidenav-container-elevation-shadow: none;
  --mat-sidenav-container-width: 360px;
  --mat-sidenav-container-divider-color: transparent;
  --mat-sidenav-container-background-color: var(--mat-sys-surface);
  --mat-sidenav-container-text-color: var(--mat-sys-on-surface-variant);
  --mat-sidenav-content-background-color: var(--mat-sys-background);
  --mat-sidenav-content-text-color: var(--mat-sys-on-background);
  --mat-sidenav-scrim-color: color-mix(in srgb, #2d3038 40%, transparent);
  --mat-sidenav-container-shape: 16px;
  --mat-sidenav-container-elevation-shadow: none;
  --mat-sidenav-container-width: 360px;
  --mat-sidenav-container-divider-color: transparent;
  --mat-stepper-header-error-state-icon-background-color: transparent;
  --mat-stepper-header-focus-state-layer-shape: 12px;
  --mat-stepper-header-hover-state-layer-shape: 12px;
  --mat-stepper-container-color: var(--mat-sys-surface);
  --mat-stepper-header-done-state-icon-background-color: var(--mat-sys-primary);
  --mat-stepper-header-done-state-icon-foreground-color: var(--mat-sys-on-primary);
  --mat-stepper-header-edit-state-icon-background-color: var(--mat-sys-primary);
  --mat-stepper-header-edit-state-icon-foreground-color: var(--mat-sys-on-primary);
  --mat-stepper-header-error-state-icon-foreground-color: var(--mat-sys-error);
  --mat-stepper-header-error-state-label-text-color: var(--mat-sys-error);
  --mat-stepper-header-focus-state-layer-color: color-mix(in srgb, var(--mat-sys-inverse-surface) 12%, transparent);
  --mat-stepper-header-hover-state-layer-color: color-mix(in srgb, var(--mat-sys-inverse-surface) 8%, transparent);
  --mat-stepper-header-icon-background-color: var(--mat-sys-on-surface-variant);
  --mat-stepper-header-icon-foreground-color: var(--mat-sys-surface);
  --mat-stepper-header-label-text-color: var(--mat-sys-on-surface-variant);
  --mat-stepper-header-optional-label-text-color: var(--mat-sys-on-surface-variant);
  --mat-stepper-header-selected-state-icon-background-color: var(--mat-sys-primary);
  --mat-stepper-header-selected-state-icon-foreground-color: var(--mat-sys-on-primary);
  --mat-stepper-header-selected-state-label-text-color: var(--mat-sys-on-surface-variant);
  --mat-stepper-line-color: var(--mat-sys-outline);
  --mat-stepper-header-height: 72px;
  --mat-stepper-container-text-font: var(--mat-sys-body-medium-font);
  --mat-stepper-header-label-text-font: var(--mat-sys-title-small-font);
  --mat-stepper-header-label-text-size: var(--mat-sys-title-small-size);
  --mat-stepper-header-label-text-weight: var(--mat-sys-title-small-weight);
  --mat-stepper-header-error-state-label-text-size: var(--mat-sys-title-small-size);
  --mat-stepper-header-selected-state-label-text-size: var(--mat-sys-title-small-size);
  --mat-stepper-header-selected-state-label-text-weight: var(--mat-sys-title-small-weight);
  --mat-stepper-header-height: 72px;
  --mat-sort-arrow-color: var(--mat-sys-on-surface);
  --mat-toolbar-container-background-color: var(--mat-sys-surface);
  --mat-toolbar-container-text-color: var(--mat-sys-on-surface);
  --mat-toolbar-standard-height: 64px;
  --mat-toolbar-mobile-height: 56px;
  --mat-toolbar-title-text-font: var(--mat-sys-title-large-font);
  --mat-toolbar-title-text-line-height: var(--mat-sys-title-large-line-height);
  --mat-toolbar-title-text-size: var(--mat-sys-title-large-size);
  --mat-toolbar-title-text-tracking: var(--mat-sys-title-large-tracking);
  --mat-toolbar-title-text-weight: var(--mat-sys-title-large-weight);
  --mat-tree-container-background-color: var(--mat-sys-surface);
  --mat-tree-node-text-color: var(--mat-sys-on-surface);
  --mat-tree-node-min-height: 48px;
  --mat-tree-node-text-font: var(--mat-sys-body-large-font);
  --mat-tree-node-text-size: var(--mat-sys-body-large-size);
  --mat-tree-node-text-weight: var(--mat-sys-body-large-weight);
  --mat-timepicker-container-shape: 4px;
  --mat-timepicker-container-background-color: var(--mat-sys-surface-container);
  --mat-timepicker-container-elevation-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
  --mat-sys-background: white;
  --mat-sys-error: #ba1a1a;
  --mat-sys-error-container: #ffdad6;
  --mat-sys-inverse-on-surface: #f2f0f4;
  --mat-sys-inverse-primary: #abc7ff;
  --mat-sys-inverse-surface: #2f3033;
  --mat-sys-on-background: #1a1b1f;
  --mat-sys-on-error: #ffffff;
  --mat-sys-on-error-container: #93000a;
  --mat-sys-on-primary: #ffffff;
  --mat-sys-on-primary-container: #00458f;
  --mat-sys-on-primary-fixed: #001b3f;
  --mat-sys-on-primary-fixed-variant: #00458f;
  --mat-sys-on-secondary: #ffffff;
  --mat-sys-on-secondary-container: #3e4759;
  --mat-sys-on-secondary-fixed: #131c2b;
  --mat-sys-on-secondary-fixed-variant: #3e4759;
  --mat-sys-on-surface: #1a1b1f;
  --mat-sys-on-surface-variant: #44474e;
  --mat-sys-on-tertiary: #ffffff;
  --mat-sys-on-tertiary-container: #0000ef;
  --mat-sys-on-tertiary-fixed: #00006e;
  --mat-sys-on-tertiary-fixed-variant: #0000ef;
  --mat-sys-outline: #74777f;
  --mat-sys-outline-variant: #c4c6d0;
  --mat-sys-primary: #005cbb;
  --mat-sys-primary-container: #d7e3ff;
  --mat-sys-primary-fixed: #d7e3ff;
  --mat-sys-primary-fixed-dim: #abc7ff;
  --mat-sys-scrim: #000000;
  --mat-sys-secondary: #565e71;
  --mat-sys-secondary-container: #dae2f9;
  --mat-sys-secondary-fixed: #dae2f9;
  --mat-sys-secondary-fixed-dim: #bec6dc;
  --mat-sys-shadow: #000000;
  --mat-sys-surface: #faf9fd;
  --mat-sys-surface-bright: #faf9fd;
  --mat-sys-surface-container: white;
  --mat-sys-surface-container-high: white;
  --mat-sys-surface-container-highest: #e3e2e6;
  --mat-sys-surface-container-low: white;
  --mat-sys-surface-container-lowest: #ffffff;
  --mat-sys-surface-dim: #dbd9dd;
  --mat-sys-surface-tint: #005cbb;
  --mat-sys-surface-variant: #e0e2ec;
  --mat-sys-tertiary: #343dff;
  --mat-sys-tertiary-container: #e0e0ff;
  --mat-sys-tertiary-fixed: #e0e0ff;
  --mat-sys-tertiary-fixed-dim: #bec2ff;
  --mat-sys-neutral-variant20: #2d3038;
  --mat-sys-neutral10: #1a1b1f;
  --mat-sys-body-large: 400 1rem / 1.5rem Roboto, sans-serif;
  --mat-sys-body-large-font: Roboto, sans-serif;
  --mat-sys-body-large-line-height: 1.5rem;
  --mat-sys-body-large-size: 1rem;
  --mat-sys-body-large-tracking: 0.031rem;
  --mat-sys-body-large-weight: 400;
  --mat-sys-body-medium: 400 0.875rem / 1.25rem Roboto, sans-serif;
  --mat-sys-body-medium-font: Roboto, sans-serif;
  --mat-sys-body-medium-line-height: 1.25rem;
  --mat-sys-body-medium-size: 0.875rem;
  --mat-sys-body-medium-tracking: 0.016rem;
  --mat-sys-body-medium-weight: 400;
  --mat-sys-body-small: 400 0.75rem / 1rem Roboto, sans-serif;
  --mat-sys-body-small-font: Roboto, sans-serif;
  --mat-sys-body-small-line-height: 1rem;
  --mat-sys-body-small-size: 0.75rem;
  --mat-sys-body-small-tracking: 0.025rem;
  --mat-sys-body-small-weight: 400;
  --mat-sys-display-large: 400 3.562rem / 4rem Roboto, sans-serif;
  --mat-sys-display-large-font: Roboto, sans-serif;
  --mat-sys-display-large-line-height: 4rem;
  --mat-sys-display-large-size: 3.562rem;
  --mat-sys-display-large-tracking: -0.016rem;
  --mat-sys-display-large-weight: 400;
  --mat-sys-display-medium: 400 2.812rem / 3.25rem Roboto, sans-serif;
  --mat-sys-display-medium-font: Roboto, sans-serif;
  --mat-sys-display-medium-line-height: 3.25rem;
  --mat-sys-display-medium-size: 2.812rem;
  --mat-sys-display-medium-tracking: 0;
  --mat-sys-display-medium-weight: 400;
  --mat-sys-display-small: 400 2.25rem / 2.75rem Roboto, sans-serif;
  --mat-sys-display-small-font: Roboto, sans-serif;
  --mat-sys-display-small-line-height: 2.75rem;
  --mat-sys-display-small-size: 2.25rem;
  --mat-sys-display-small-tracking: 0;
  --mat-sys-display-small-weight: 400;
  --mat-sys-headline-large: 400 2rem / 2.5rem Roboto, sans-serif;
  --mat-sys-headline-large-font: Roboto, sans-serif;
  --mat-sys-headline-large-line-height: 2.5rem;
  --mat-sys-headline-large-size: 2rem;
  --mat-sys-headline-large-tracking: 0;
  --mat-sys-headline-large-weight: 400;
  --mat-sys-headline-medium: 400 1.75rem / 2.25rem Roboto, sans-serif;
  --mat-sys-headline-medium-font: Roboto, sans-serif;
  --mat-sys-headline-medium-line-height: 2.25rem;
  --mat-sys-headline-medium-size: 1.75rem;
  --mat-sys-headline-medium-tracking: 0;
  --mat-sys-headline-medium-weight: 400;
  --mat-sys-headline-small: 400 1.5rem / 2rem Roboto, sans-serif;
  --mat-sys-headline-small-font: Roboto, sans-serif;
  --mat-sys-headline-small-line-height: 2rem;
  --mat-sys-headline-small-size: 1.5rem;
  --mat-sys-headline-small-tracking: 0;
  --mat-sys-headline-small-weight: 400;
  --mat-sys-label-large: 500 0.875rem / 1.25rem Roboto, sans-serif;
  --mat-sys-label-large-font: Roboto, sans-serif;
  --mat-sys-label-large-line-height: 1.25rem;
  --mat-sys-label-large-size: 0.875rem;
  --mat-sys-label-large-tracking: 0.006rem;
  --mat-sys-label-large-weight: 500;
  --mat-sys-label-large-weight-prominent: 700;
  --mat-sys-label-medium: 500 0.75rem / 1rem Roboto, sans-serif;
  --mat-sys-label-medium-font: Roboto, sans-serif;
  --mat-sys-label-medium-line-height: 1rem;
  --mat-sys-label-medium-size: 0.75rem;
  --mat-sys-label-medium-tracking: 0.031rem;
  --mat-sys-label-medium-weight: 500;
  --mat-sys-label-medium-weight-prominent: 700;
  --mat-sys-label-small: 500 0.688rem / 1rem Roboto, sans-serif;
  --mat-sys-label-small-font: Roboto, sans-serif;
  --mat-sys-label-small-line-height: 1rem;
  --mat-sys-label-small-size: 0.688rem;
  --mat-sys-label-small-tracking: 0.031rem;
  --mat-sys-label-small-weight: 500;
  --mat-sys-title-large: 400 1.375rem / 1.75rem Roboto, sans-serif;
  --mat-sys-title-large-font: Roboto, sans-serif;
  --mat-sys-title-large-line-height: 1.75rem;
  --mat-sys-title-large-size: 1.375rem;
  --mat-sys-title-large-tracking: 0;
  --mat-sys-title-large-weight: 400;
  --mat-sys-title-medium: 500 1rem / 1.5rem Roboto, sans-serif;
  --mat-sys-title-medium-font: Roboto, sans-serif;
  --mat-sys-title-medium-line-height: 1.5rem;
  --mat-sys-title-medium-size: 1rem;
  --mat-sys-title-medium-tracking: 0.009rem;
  --mat-sys-title-medium-weight: 500;
  --mat-sys-title-small: 500 0.875rem / 1.25rem Roboto, sans-serif;
  --mat-sys-title-small-font: Roboto, sans-serif;
  --mat-sys-title-small-line-height: 1.25rem;
  --mat-sys-title-small-size: 0.875rem;
  --mat-sys-title-small-tracking: 0.006rem;
  --mat-sys-title-small-weight: 500;
  --mat-dialog-container-max-width: 90vw;
}

.mat-display-large, .mat-typography .mat-display-large, .mat-typography h1 {
  font: var(--mat-sys-display-large);
  letter-spacing: var(--mat-sys-display-large-tracking);
  margin: 0 0 0.5em;
}

.mat-display-medium, .mat-typography .mat-display-medium, .mat-typography h2 {
  font: var(--mat-sys-display-medium);
  letter-spacing: var(--mat-sys-display-medium-tracking);
  margin: 0 0 0.5em;
}

.mat-display-small, .mat-typography .mat-display-small, .mat-typography h3 {
  font: var(--mat-sys-display-small);
  letter-spacing: var(--mat-sys-display-small-tracking);
  margin: 0 0 0.5em;
}

.mat-headline-large, .mat-typography .mat-headline-large, .mat-typography h4 {
  font: var(--mat-sys-headline-large);
  letter-spacing: var(--mat-sys-headline-large-tracking);
  margin: 0 0 0.5em;
}

.mat-headline-medium, .mat-typography .mat-headline-medium, .mat-typography h5 {
  font: var(--mat-sys-headline-medium);
  letter-spacing: var(--mat-sys-headline-medium-tracking);
  margin: 0 0 0.5em;
}

.mat-headline-small, .mat-typography .mat-headline-small, .mat-typography h6 {
  font: var(--mat-sys-headline-small);
  letter-spacing: var(--mat-sys-headline-small-tracking);
  margin: 0 0 0.5em;
}

.mat-title-large, .mat-typography .mat-title-large {
  font: var(--mat-sys-title-large);
  letter-spacing: var(--mat-sys-title-large-tracking);
}

.mat-title-medium, .mat-typography .mat-title-medium {
  font: var(--mat-sys-title-medium);
  letter-spacing: var(--mat-sys-title-medium-tracking);
}

.mat-title-small, .mat-typography .mat-title-small {
  font: var(--mat-sys-title-small);
  letter-spacing: var(--mat-sys-title-small-tracking);
}

.mat-body-large, .mat-typography .mat-body-large, .mat-typography {
  font: var(--mat-sys-body-large);
  letter-spacing: var(--mat-sys-body-large-tracking);
}

.mat-body-large p, .mat-typography .mat-body-large p, .mat-typography p {
  margin: 0 0 0.75em;
}

.mat-body-medium, .mat-typography .mat-body-medium {
  font: var(--mat-sys-body-medium);
  letter-spacing: var(--mat-sys-body-medium-tracking);
}

.mat-body-small, .mat-typography .mat-body-small {
  font: var(--mat-sys-body-small);
  letter-spacing: var(--mat-sys-body-small-tracking);
}

.mat-label-large, .mat-typography .mat-label-large {
  font: var(--mat-sys-label-large);
  letter-spacing: var(--mat-sys-label-large-tracking);
}

.mat-label-medium, .mat-typography .mat-label-medium {
  font: var(--mat-sys-label-medium);
  letter-spacing: var(--mat-sys-label-medium-tracking);
}

.mat-label-small, .mat-typography .mat-label-small {
  font: var(--mat-sys-label-small);
  letter-spacing: var(--mat-sys-label-small-tracking);
}

.mat-mdc-raised-button.mat-primary {
  --mat-button-protected-container-color: var(--mat-sys-primary);
  --mat-button-protected-label-text-color: white;
  --mat-button-protected-state-layer-color: white;
  --mat-button-protected-ripple-color: rgba(255, 255, 255, 0.1) ;
}

.mat-mdc-raised-button.mat-warn {
  --mat-button-protected-container-color: var(--mat-sys-error);
  --mat-button-protected-label-text-color: white;
  --mat-button-protected-state-layer-color: white;
  --mat-button-protected-ripple-color: rgba(255, 255, 255, 0.1) ;
}

ecui-sidenav-layout {
  --mat-sidenav-container-shape: 0px;
  --mat-sidenav-container-width: auto;
}

.mat-datepicker-content {
  box-shadow: 1px 1px 1px silver !important;
}

/*# sourceMappingURL=m3-appstyles.css.map */
