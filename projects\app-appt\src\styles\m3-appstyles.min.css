html{--mat-app-background-color:var(--sys-background);--mat-app-text-color:var(--sys-on-background);--mat-app-elevation-shadow-level-0:0px 0px 0px 0px rgba(0, 0, 0, 0.2),0px 0px 0px 0px rgba(0, 0, 0, 0.14),0px 0px 0px 0px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-1:0px 2px 1px -1px rgba(0, 0, 0, 0.2),0px 1px 1px 0px rgba(0, 0, 0, 0.14),0px 1px 3px 0px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-2:0px 3px 1px -2px rgba(0, 0, 0, 0.2),0px 2px 2px 0px rgba(0, 0, 0, 0.14),0px 1px 5px 0px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-3:0px 3px 3px -2px rgba(0, 0, 0, 0.2),0px 3px 4px 0px rgba(0, 0, 0, 0.14),0px 1px 8px 0px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-4:0px 2px 4px -1px rgba(0, 0, 0, 0.2),0px 4px 5px 0px rgba(0, 0, 0, 0.14),0px 1px 10px 0px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-5:0px 3px 5px -1px rgba(0, 0, 0, 0.2),0px 5px 8px 0px rgba(0, 0, 0, 0.14),0px 1px 14px 0px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-6:0px 3px 5px -1px rgba(0, 0, 0, 0.2),0px 6px 10px 0px rgba(0, 0, 0, 0.14),0px 1px 18px 0px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-7:0px 4px 5px -2px rgba(0, 0, 0, 0.2),0px 7px 10px 1px rgba(0, 0, 0, 0.14),0px 2px 16px 1px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-8:0px 5px 5px -3px rgba(0, 0, 0, 0.2),0px 8px 10px 1px rgba(0, 0, 0, 0.14),0px 3px 14px 2px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-9:0px 5px 6px -3px rgba(0, 0, 0, 0.2),0px 9px 12px 1px rgba(0, 0, 0, 0.14),0px 3px 16px 2px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-10:0px 6px 6px -3px rgba(0, 0, 0, 0.2),0px 10px 14px 1px rgba(0, 0, 0, 0.14),0px 4px 18px 3px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-11:0px 6px 7px -4px rgba(0, 0, 0, 0.2),0px 11px 15px 1px rgba(0, 0, 0, 0.14),0px 4px 20px 3px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-12:0px 7px 8px -4px rgba(0, 0, 0, 0.2),0px 12px 17px 2px rgba(0, 0, 0, 0.14),0px 5px 22px 4px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-13:0px 7px 8px -4px rgba(0, 0, 0, 0.2),0px 13px 19px 2px rgba(0, 0, 0, 0.14),0px 5px 24px 4px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-14:0px 7px 9px -4px rgba(0, 0, 0, 0.2),0px 14px 21px 2px rgba(0, 0, 0, 0.14),0px 5px 26px 4px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-15:0px 8px 9px -5px rgba(0, 0, 0, 0.2),0px 15px 22px 2px rgba(0, 0, 0, 0.14),0px 6px 28px 5px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-16:0px 8px 10px -5px rgba(0, 0, 0, 0.2),0px 16px 24px 2px rgba(0, 0, 0, 0.14),0px 6px 30px 5px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-17:0px 8px 11px -5px rgba(0, 0, 0, 0.2),0px 17px 26px 2px rgba(0, 0, 0, 0.14),0px 6px 32px 5px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-18:0px 9px 11px -5px rgba(0, 0, 0, 0.2),0px 18px 28px 2px rgba(0, 0, 0, 0.14),0px 7px 34px 6px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-19:0px 9px 12px -6px rgba(0, 0, 0, 0.2),0px 19px 29px 2px rgba(0, 0, 0, 0.14),0px 7px 36px 6px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-20:0px 10px 13px -6px rgba(0, 0, 0, 0.2),0px 20px 31px 3px rgba(0, 0, 0, 0.14),0px 8px 38px 7px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-21:0px 10px 13px -6px rgba(0, 0, 0, 0.2),0px 21px 33px 3px rgba(0, 0, 0, 0.14),0px 8px 40px 7px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-22:0px 10px 14px -6px rgba(0, 0, 0, 0.2),0px 22px 35px 3px rgba(0, 0, 0, 0.14),0px 8px 42px 7px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-23:0px 11px 14px -7px rgba(0, 0, 0, 0.2),0px 23px 36px 3px rgba(0, 0, 0, 0.14),0px 9px 44px 8px rgba(0, 0, 0, 0.12);--mat-app-elevation-shadow-level-24:0px 11px 15px -7px rgba(0, 0, 0, 0.2),0px 24px 38px 3px rgba(0, 0, 0, 0.14),0px 9px 46px 8px rgba(0, 0, 0, 0.12);--mat-ripple-color:color-mix(in srgb, var(--sys-on-surface) 10%, transparent);--mat-option-selected-state-label-text-color:var(--sys-on-secondary-container);--mat-option-label-text-color:var(--sys-on-surface);--mat-option-hover-state-layer-color:color-mix(in srgb, var(--sys-on-surface) 8%, transparent);--mat-option-focus-state-layer-color:color-mix(in srgb, var(--sys-on-surface) 12%, transparent);--mat-option-selected-state-layer-color:var(--sys-secondary-container);--mat-option-label-text-font:var(--sys-label-large-font);--mat-option-label-text-line-height:var(--sys-label-large-line-height);--mat-option-label-text-size:var(--sys-body-large-size);--mat-option-label-text-tracking:var(--sys-label-large-tracking);--mat-option-label-text-weight:var(--sys-body-large-weight);--mat-optgroup-label-text-color:var(--sys-on-surface-variant);--mat-optgroup-label-text-font:var(--sys-title-small-font);--mat-optgroup-label-text-line-height:var(--sys-title-small-line-height);--mat-optgroup-label-text-size:var(--sys-title-small-size);--mat-optgroup-label-text-tracking:var(--sys-title-small-tracking);--mat-optgroup-label-text-weight:var(--sys-title-small-weight);--mat-full-pseudo-checkbox-selected-icon-color:var(--sys-primary);--mat-full-pseudo-checkbox-selected-checkmark-color:var(--sys-on-primary);--mat-full-pseudo-checkbox-unselected-icon-color:var(--sys-on-surface-variant);--mat-full-pseudo-checkbox-disabled-selected-checkmark-color:var(--sys-surface);--mat-full-pseudo-checkbox-disabled-unselected-icon-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mat-full-pseudo-checkbox-disabled-selected-icon-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mat-minimal-pseudo-checkbox-selected-checkmark-color:var(--sys-primary);--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mdc-elevated-card-container-color:var(--sys-surface);--mdc-elevated-card-container-elevation:0px 2px 1px -1px rgba(0, 0, 0, 0.2),0px 1px 1px 0px rgba(0, 0, 0, 0.14),0px 1px 3px 0px rgba(0, 0, 0, 0.12);--mdc-elevated-card-container-shape:12px;--mdc-outlined-card-container-color:var(--sys-surface);--mdc-outlined-card-outline-color:var(--sys-outline-variant);--mdc-outlined-card-container-elevation:0px 0px 0px 0px rgba(0, 0, 0, 0.2),0px 0px 0px 0px rgba(0, 0, 0, 0.14),0px 0px 0px 0px rgba(0, 0, 0, 0.12);--mdc-outlined-card-container-shape:12px;--mdc-outlined-card-outline-width:1px;--mat-card-subtitle-text-color:var(--sys-on-surface);--mat-card-title-text-font:var(--sys-title-large-font);--mat-card-title-text-line-height:var(--sys-title-large-line-height);--mat-card-title-text-size:var(--sys-title-large-size);--mat-card-title-text-tracking:var(--sys-title-large-tracking);--mat-card-title-text-weight:var(--sys-title-large-weight);--mat-card-subtitle-text-font:var(--sys-title-medium-font);--mat-card-subtitle-text-line-height:var(--sys-title-medium-line-height);--mat-card-subtitle-text-size:var(--sys-title-medium-size);--mat-card-subtitle-text-tracking:var(--sys-title-medium-tracking);--mat-card-subtitle-text-weight:var(--sys-title-medium-weight);--mdc-linear-progress-active-indicator-color:var(--sys-primary);--mdc-linear-progress-track-color:var(--sys-surface-variant);--mdc-linear-progress-active-indicator-height:4px;--mdc-linear-progress-track-height:4px;--mdc-linear-progress-track-shape:0px;--mdc-plain-tooltip-container-color:var(--sys-inverse-surface);--mdc-plain-tooltip-supporting-text-color:var(--sys-inverse-on-surface);--mdc-plain-tooltip-supporting-text-line-height:var(--sys-body-small-line-height);--mdc-plain-tooltip-supporting-text-font:var(--sys-body-small-font);--mdc-plain-tooltip-supporting-text-size:var(--sys-body-small-size);--mdc-plain-tooltip-supporting-text-weight:var(--sys-body-small-weight);--mdc-plain-tooltip-supporting-text-tracking:var(--sys-body-small-tracking);--mdc-plain-tooltip-container-shape:4px;--mdc-filled-text-field-caret-color:var(--sys-primary);--mdc-filled-text-field-focus-active-indicator-color:var(--sys-primary);--mdc-filled-text-field-focus-label-text-color:var(--sys-primary);--mdc-filled-text-field-container-color:var(--sys-surface-variant);--mdc-filled-text-field-disabled-container-color:color-mix(in srgb, var(--sys-on-surface) 4%, transparent);--mdc-filled-text-field-label-text-color:var(--sys-on-surface-variant);--mdc-filled-text-field-hover-label-text-color:var(--sys-on-surface-variant);--mdc-filled-text-field-disabled-label-text-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mdc-filled-text-field-input-text-color:var(--sys-on-surface);--mdc-filled-text-field-disabled-input-text-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mdc-filled-text-field-input-text-placeholder-color:var(--sys-on-surface-variant);--mdc-filled-text-field-error-hover-label-text-color:var(--sys-on-error-container);--mdc-filled-text-field-error-focus-label-text-color:var(--sys-error);--mdc-filled-text-field-error-label-text-color:var(--sys-error);--mdc-filled-text-field-active-indicator-color:var(--sys-on-surface-variant);--mdc-filled-text-field-disabled-active-indicator-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mdc-filled-text-field-hover-active-indicator-color:var(--sys-on-surface);--mdc-filled-text-field-error-active-indicator-color:var(--sys-error);--mdc-filled-text-field-error-focus-active-indicator-color:var(--sys-error);--mdc-filled-text-field-error-hover-active-indicator-color:var(--sys-on-error-container);--mdc-filled-text-field-label-text-font:var(--sys-body-large-font);--mdc-filled-text-field-label-text-size:var(--sys-body-large-size);--mdc-filled-text-field-label-text-tracking:var(--sys-body-large-tracking);--mdc-filled-text-field-label-text-weight:var(--sys-body-large-weight);--mdc-filled-text-field-active-indicator-height:1px;--mdc-filled-text-field-focus-active-indicator-height:2px;--mdc-filled-text-field-container-shape:4px;--mdc-outlined-text-field-caret-color:var(--sys-primary);--mdc-outlined-text-field-focus-outline-color:var(--sys-primary);--mdc-outlined-text-field-focus-label-text-color:var(--sys-primary);--mdc-outlined-text-field-label-text-color:var(--sys-on-surface-variant);--mdc-outlined-text-field-hover-label-text-color:var(--sys-on-surface);--mdc-outlined-text-field-disabled-label-text-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mdc-outlined-text-field-input-text-color:var(--sys-on-surface);--mdc-outlined-text-field-disabled-input-text-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mdc-outlined-text-field-input-text-placeholder-color:var(--sys-on-surface-variant);--mdc-outlined-text-field-error-focus-label-text-color:var(--sys-error);--mdc-outlined-text-field-error-label-text-color:var(--sys-error);--mdc-outlined-text-field-error-hover-label-text-color:var(--sys-on-error-container);--mdc-outlined-text-field-outline-color:var(--sys-outline);--mdc-outlined-text-field-disabled-outline-color:color-mix(in srgb, var(--sys-on-surface) 12%, transparent);--mdc-outlined-text-field-hover-outline-color:var(--sys-on-surface);--mdc-outlined-text-field-error-focus-outline-color:var(--sys-error);--mdc-outlined-text-field-error-hover-outline-color:var(--sys-on-error-container);--mdc-outlined-text-field-error-outline-color:var(--sys-error);--mdc-outlined-text-field-label-text-font:var(--sys-body-large-font);--mdc-outlined-text-field-label-text-size:var(--sys-body-large-size);--mdc-outlined-text-field-label-text-tracking:var(--sys-body-large-tracking);--mdc-outlined-text-field-label-text-weight:var(--sys-body-large-weight);--mdc-outlined-text-field-outline-width:1px;--mdc-outlined-text-field-focus-outline-width:2px;--mdc-outlined-text-field-container-shape:4px;--mat-form-field-focus-select-arrow-color:var(--sys-primary);--mat-form-field-disabled-input-text-placeholder-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mat-form-field-state-layer-color:var(--sys-on-surface);--mat-form-field-error-text-color:var(--sys-error);--mat-form-field-select-option-text-color:#1a1b1f;--mat-form-field-select-disabled-option-text-color:rgba(26, 27, 31, 0.38);--mat-form-field-leading-icon-color:var(--sys-on-surface-variant);--mat-form-field-disabled-leading-icon-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mat-form-field-trailing-icon-color:var(--sys-on-surface-variant);--mat-form-field-disabled-trailing-icon-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mat-form-field-error-focus-trailing-icon-color:var(--sys-error);--mat-form-field-error-hover-trailing-icon-color:var(--sys-on-error-container);--mat-form-field-error-trailing-icon-color:var(--sys-error);--mat-form-field-enabled-select-arrow-color:var(--sys-on-surface-variant);--mat-form-field-disabled-select-arrow-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mat-form-field-hover-state-layer-opacity:0.08;--mat-form-field-container-text-font:var(--sys-body-large-font);--mat-form-field-container-text-line-height:var(--sys-body-large-line-height);--mat-form-field-container-text-size:var(--sys-body-large-size);--mat-form-field-container-text-tracking:var(--sys-body-large-tracking);--mat-form-field-container-text-weight:var(--sys-body-large-weight);--mat-form-field-subscript-text-font:var(--sys-body-small-font);--mat-form-field-subscript-text-line-height:var(--sys-body-small-line-height);--mat-form-field-subscript-text-size:var(--sys-body-small-size);--mat-form-field-subscript-text-tracking:var(--sys-body-small-tracking);--mat-form-field-subscript-text-weight:var(--sys-body-small-weight);--mat-form-field-container-height:56px;--mat-form-field-filled-label-display:block;--mat-form-field-container-vertical-padding:16px;--mat-form-field-filled-with-label-container-padding-top:24px;--mat-form-field-filled-with-label-container-padding-bottom:8px;--mat-form-field-focus-state-layer-opacity:0;--mat-select-panel-background-color:var(--sys-surface-container);--mat-select-enabled-trigger-text-color:var(--sys-on-surface);--mat-select-disabled-trigger-text-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mat-select-placeholder-text-color:var(--sys-on-surface-variant);--mat-select-enabled-arrow-color:var(--sys-on-surface-variant);--mat-select-disabled-arrow-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mat-select-focused-arrow-color:var(--sys-primary);--mat-select-invalid-arrow-color:var(--sys-error);--mat-select-trigger-text-font:var(--sys-body-large-font);--mat-select-trigger-text-line-height:var(--sys-body-large-line-height);--mat-select-trigger-text-size:var(--sys-body-large-size);--mat-select-trigger-text-tracking:var(--sys-body-large-tracking);--mat-select-trigger-text-weight:var(--sys-body-large-weight);--mat-select-arrow-transform:translateY(-8px);--mat-select-container-elevation-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2),0px 2px 2px 0px rgba(0, 0, 0, 0.14),0px 1px 5px 0px rgba(0, 0, 0, 0.12);--mat-autocomplete-background-color:var(--sys-surface-container);--mat-autocomplete-container-shape:4px;--mat-autocomplete-container-elevation-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2),0px 2px 2px 0px rgba(0, 0, 0, 0.14),0px 1px 5px 0px rgba(0, 0, 0, 0.12);--mdc-dialog-container-color:var(--sys-surface);--mdc-dialog-subhead-color:var(--sys-on-surface);--mdc-dialog-supporting-text-color:var(--sys-on-surface-variant);--mdc-dialog-subhead-font:var(--sys-headline-small-font);--mdc-dialog-subhead-line-height:var(--sys-headline-small-line-height);--mdc-dialog-subhead-size:var(--sys-headline-small-size);--mdc-dialog-subhead-weight:var(--sys-headline-small-weight);--mdc-dialog-subhead-tracking:var(--sys-headline-small-tracking);--mdc-dialog-supporting-text-font:var(--sys-body-medium-font);--mdc-dialog-supporting-text-line-height:var(--sys-body-medium-line-height);--mdc-dialog-supporting-text-size:var(--sys-body-medium-size);--mdc-dialog-supporting-text-weight:var(--sys-body-medium-weight);--mdc-dialog-supporting-text-tracking:var(--sys-body-medium-tracking);--mdc-dialog-container-shape:28px;--mat-dialog-container-elevation-shadow:none;--mat-dialog-container-max-width:560px;--mat-dialog-container-small-max-width:calc(100vw - 32px);--mat-dialog-container-min-width:280px;--mat-dialog-actions-alignment:flex-end;--mat-dialog-actions-padding:16px 24px;--mat-dialog-content-padding:20px 24px;--mat-dialog-with-actions-content-padding:20px 24px 0;--mat-dialog-headline-padding:6px 24px 13px;--mdc-chip-outline-color:var(--sys-outline);--mdc-chip-disabled-outline-color:color-mix(in srgb, var(--sys-on-surface) 12%, transparent);--mdc-chip-focus-outline-color:var(--sys-on-surface-variant);--mdc-chip-hover-state-layer-opacity:0.08;--mdc-chip-selected-hover-state-layer-opacity:0.08;--mdc-chip-disabled-label-text-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mdc-chip-elevated-selected-container-color:var(--sys-secondary-container);--mdc-chip-flat-disabled-selected-container-color:color-mix(in srgb, var(--sys-on-surface) 12%, transparent);--mdc-chip-focus-state-layer-color:var(--sys-on-surface-variant);--mdc-chip-hover-state-layer-color:var(--sys-on-surface-variant);--mdc-chip-selected-hover-state-layer-color:var(--sys-on-secondary-container);--mdc-chip-focus-state-layer-opacity:0.12;--mdc-chip-selected-focus-state-layer-color:var(--sys-on-secondary-container);--mdc-chip-selected-focus-state-layer-opacity:0.12;--mdc-chip-label-text-color:var(--sys-on-surface-variant);--mdc-chip-selected-label-text-color:var(--sys-on-secondary-container);--mdc-chip-with-icon-icon-color:var(--sys-on-surface-variant);--mdc-chip-with-icon-disabled-icon-color:var(--sys-on-surface);--mdc-chip-with-icon-selected-icon-color:var(--sys-on-secondary-container);--mdc-chip-with-trailing-icon-disabled-trailing-icon-color:var(--sys-on-surface);--mdc-chip-with-trailing-icon-trailing-icon-color:var(--sys-on-surface-variant);--mdc-chip-label-text-font:var(--sys-label-large-font);--mdc-chip-label-text-line-height:var(--sys-label-large-line-height);--mdc-chip-label-text-size:var(--sys-label-large-size);--mdc-chip-label-text-tracking:var(--sys-label-large-tracking);--mdc-chip-label-text-weight:var(--sys-label-large-weight);--mdc-chip-container-height:32px;--mdc-chip-container-shape-family:rounded;--mdc-chip-container-shape-radius:8px;--mdc-chip-with-avatar-avatar-size:24px;--mdc-chip-with-icon-icon-size:18px;--mdc-chip-outline-width:1px;--mdc-chip-with-avatar-disabled-avatar-opacity:0.38;--mdc-chip-flat-selected-outline-width:0;--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity:0.38;--mdc-chip-with-icon-disabled-icon-opacity:0.38;--mat-chip-trailing-action-state-layer-color:var(--sys-on-surface-variant);--mat-chip-selected-trailing-action-state-layer-color:var(--sys-on-secondary-container);--mat-chip-trailing-action-hover-state-layer-opacity:0.08;--mat-chip-trailing-action-focus-state-layer-opacity:0.12;--mat-chip-selected-disabled-trailing-icon-color:var(--sys-on-surface);--mat-chip-selected-trailing-icon-color:var(--sys-on-secondary-container);--mat-chip-disabled-container-opacity:1;--mat-chip-trailing-action-opacity:1;--mat-chip-trailing-action-focus-opacity:1;--mdc-switch-selected-focus-state-layer-opacity:0.12;--mdc-switch-selected-hover-state-layer-opacity:0.08;--mdc-switch-selected-pressed-state-layer-opacity:0.12;--mdc-switch-unselected-focus-state-layer-opacity:0.12;--mdc-switch-unselected-hover-state-layer-opacity:0.08;--mdc-switch-unselected-pressed-state-layer-opacity:0.12;--mdc-switch-selected-focus-state-layer-color:var(--sys-primary);--mdc-switch-selected-handle-color:var(--sys-on-primary);--mdc-switch-selected-hover-state-layer-color:var(--sys-primary);--mdc-switch-selected-pressed-state-layer-color:var(--sys-primary);--mdc-switch-selected-focus-handle-color:var(--sys-primary-container);--mdc-switch-selected-hover-handle-color:var(--sys-primary-container);--mdc-switch-selected-pressed-handle-color:var(--sys-primary-container);--mdc-switch-selected-focus-track-color:var(--sys-primary);--mdc-switch-selected-hover-track-color:var(--sys-primary);--mdc-switch-selected-pressed-track-color:var(--sys-primary);--mdc-switch-selected-track-color:var(--sys-primary);--mdc-switch-disabled-selected-handle-color:var(--sys-surface);--mdc-switch-disabled-selected-icon-color:var(--sys-on-surface);--mdc-switch-disabled-selected-track-color:var(--sys-on-surface);--mdc-switch-disabled-unselected-handle-color:var(--sys-on-surface);--mdc-switch-disabled-unselected-icon-color:var(--sys-surface-variant);--mdc-switch-disabled-unselected-track-color:var(--sys-surface-variant);--mdc-switch-selected-icon-color:var(--sys-on-primary-container);--mdc-switch-unselected-focus-handle-color:var(--sys-on-surface-variant);--mdc-switch-unselected-focus-state-layer-color:var(--sys-on-surface);--mdc-switch-unselected-focus-track-color:var(--sys-surface-variant);--mdc-switch-unselected-handle-color:var(--sys-outline);--mdc-switch-unselected-hover-handle-color:var(--sys-on-surface-variant);--mdc-switch-unselected-hover-state-layer-color:var(--sys-on-surface);--mdc-switch-unselected-hover-track-color:var(--sys-surface-variant);--mdc-switch-unselected-icon-color:var(--sys-surface-variant);--mdc-switch-unselected-pressed-handle-color:var(--sys-on-surface-variant);--mdc-switch-unselected-pressed-state-layer-color:var(--sys-on-surface);--mdc-switch-unselected-pressed-track-color:var(--sys-surface-variant);--mdc-switch-unselected-track-color:var(--sys-surface-variant);--mdc-switch-disabled-selected-icon-opacity:0.38;--mdc-switch-disabled-track-opacity:0.12;--mdc-switch-disabled-unselected-icon-opacity:0.38;--mdc-switch-handle-shape:9999px;--mdc-switch-selected-icon-size:16px;--mdc-switch-track-height:32px;--mdc-switch-track-shape:9999px;--mdc-switch-track-width:52px;--mdc-switch-unselected-icon-size:16px;--mdc-switch-state-layer-size:40px;--mat-switch-track-outline-color:var(--sys-outline);--mat-switch-disabled-unselected-track-outline-color:var(--sys-on-surface);--mat-switch-label-text-color:var(--sys-on-surface);--mat-switch-label-text-font:var(--sys-body-medium-font);--mat-switch-label-text-line-height:var(--sys-body-medium-line-height);--mat-switch-label-text-size:var(--sys-body-medium-size);--mat-switch-label-text-tracking:var(--sys-body-medium-tracking);--mat-switch-label-text-weight:var(--sys-body-medium-weight);--mat-switch-disabled-selected-handle-opacity:1;--mat-switch-disabled-unselected-handle-opacity:0.38;--mat-switch-unselected-handle-size:16px;--mat-switch-selected-handle-size:24px;--mat-switch-pressed-handle-size:28px;--mat-switch-with-icon-handle-size:24px;--mat-switch-selected-handle-horizontal-margin:0 24px;--mat-switch-selected-with-icon-handle-horizontal-margin:0 24px;--mat-switch-selected-pressed-handle-horizontal-margin:0 22px;--mat-switch-unselected-handle-horizontal-margin:0 8px;--mat-switch-unselected-with-icon-handle-horizontal-margin:0 4px;--mat-switch-unselected-pressed-handle-horizontal-margin:0 2px;--mat-switch-visible-track-opacity:1;--mat-switch-hidden-track-opacity:0;--mat-switch-visible-track-transition:opacity 75ms;--mat-switch-hidden-track-transition:opacity 75ms;--mat-switch-track-outline-width:2px;--mat-switch-selected-track-outline-width:2px;--mat-switch-selected-track-outline-color:transparent;--mat-switch-disabled-unselected-track-outline-width:2px;--mdc-radio-disabled-selected-icon-color:var(--sys-on-surface);--mdc-radio-disabled-unselected-icon-color:var(--sys-on-surface);--mdc-radio-unselected-hover-icon-color:var(--sys-on-surface);--mdc-radio-unselected-icon-color:var(--sys-on-surface-variant);--mdc-radio-unselected-pressed-icon-color:var(--sys-on-surface);--mdc-radio-selected-focus-icon-color:var(--sys-primary);--mdc-radio-selected-hover-icon-color:var(--sys-primary);--mdc-radio-selected-icon-color:var(--sys-primary);--mdc-radio-selected-pressed-icon-color:var(--sys-primary);--mdc-radio-state-layer-size:40px;--mdc-radio-disabled-selected-icon-opacity:0.38;--mdc-radio-disabled-unselected-icon-opacity:0.38;--mat-radio-ripple-color:var(--sys-on-surface);--mat-radio-checked-ripple-color:var(--sys-primary);--mat-radio-disabled-label-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mat-radio-label-text-color:var(--sys-on-surface);--mat-radio-label-text-font:var(--sys-body-medium-font);--mat-radio-label-text-line-height:var(--sys-body-medium-line-height);--mat-radio-label-text-size:var(--sys-body-medium-size);--mat-radio-label-text-tracking:var(--sys-body-medium-tracking);--mat-radio-label-text-weight:var(--sys-body-medium-weight);--mat-radio-touch-target-display:block;--mdc-slider-handle-color:var(--sys-primary);--mdc-slider-focus-handle-color:var(--sys-primary);--mdc-slider-hover-handle-color:var(--sys-primary);--mdc-slider-active-track-color:var(--sys-primary);--mdc-slider-inactive-track-color:var(--sys-surface-variant);--mdc-slider-with-tick-marks-inactive-container-color:var(--sys-on-surface-variant);--mdc-slider-with-tick-marks-active-container-color:var(--sys-on-primary);--mdc-slider-disabled-active-track-color:var(--sys-on-surface);--mdc-slider-disabled-handle-color:var(--sys-on-surface);--mdc-slider-disabled-inactive-track-color:var(--sys-on-surface);--mdc-slider-label-container-color:var(--sys-primary);--mdc-slider-label-label-text-color:var(--sys-on-primary);--mdc-slider-with-overlap-handle-outline-color:var(--sys-on-primary);--mdc-slider-with-tick-marks-disabled-container-color:var(--sys-on-surface);--mdc-slider-handle-elevation:1;--mdc-slider-handle-shadow-color:#000000;--mdc-slider-label-label-text-font:var(--sys-label-medium-font);--mdc-slider-label-label-text-size:var(--sys-label-medium-size);--mdc-slider-label-label-text-line-height:var(--sys-label-medium-line-height);--mdc-slider-label-label-text-tracking:var(--sys-label-medium-tracking);--mdc-slider-label-label-text-weight:var(--sys-label-medium-weight);--mdc-slider-active-track-height:4px;--mdc-slider-active-track-shape:9999px;--mdc-slider-handle-height:20px;--mdc-slider-handle-shape:9999px;--mdc-slider-handle-width:20px;--mdc-slider-inactive-track-height:4px;--mdc-slider-inactive-track-shape:9999px;--mdc-slider-with-overlap-handle-outline-width:1px;--mdc-slider-with-tick-marks-active-container-opacity:0.38;--mdc-slider-with-tick-marks-container-shape:9999px;--mdc-slider-with-tick-marks-container-size:2px;--mdc-slider-with-tick-marks-inactive-container-opacity:0.38;--mat-slider-ripple-color:var(--sys-primary);--mat-slider-hover-state-layer-color:color-mix(in srgb, var(--sys-primary) 5%, transparent);--mat-slider-focus-state-layer-color:color-mix(in srgb, var(--sys-primary) 20%, transparent);--mat-slider-value-indicator-width:28px;--mat-slider-value-indicator-height:28px;--mat-slider-value-indicator-caret-display:none;--mat-slider-value-indicator-border-radius:50% 50% 50% 0;--mat-slider-value-indicator-padding:0;--mat-slider-value-indicator-text-transform:rotate(45deg);--mat-slider-value-indicator-container-transform:translateX(-50%) rotate(-45deg);--mat-slider-value-indicator-opacity:1;--mat-menu-item-label-text-color:var(--sys-on-surface);--mat-menu-item-icon-color:var(--sys-on-surface-variant);--mat-menu-item-hover-state-layer-color:color-mix(in srgb, var(--sys-on-surface) 8%, transparent);--mat-menu-item-focus-state-layer-color:color-mix(in srgb, var(--sys-on-surface) 12%, transparent);--mat-menu-container-color:var(--sys-surface-container);--mat-menu-divider-color:var(--sys-surface-variant);--mat-menu-item-label-text-font:var(--sys-label-large-font);--mat-menu-item-label-text-size:var(--sys-label-large-size);--mat-menu-item-label-text-tracking:var(--sys-label-large-tracking);--mat-menu-item-label-text-line-height:var(--sys-label-large-line-height);--mat-menu-item-label-text-weight:var(--sys-label-large-weight);--mat-menu-container-shape:4px;--mat-menu-divider-bottom-spacing:8px;--mat-menu-divider-top-spacing:8px;--mat-menu-item-spacing:12px;--mat-menu-item-icon-size:24px;--mat-menu-item-leading-spacing:12px;--mat-menu-item-trailing-spacing:12px;--mat-menu-item-with-icon-leading-spacing:12px;--mat-menu-item-with-icon-trailing-spacing:12px;--mat-menu-base-elevation-level:2;--mdc-list-list-item-container-color:transparent;--mdc-list-list-item-leading-avatar-color:var(--sys-primary-container);--mdc-list-list-item-disabled-state-layer-color:var(--sys-on-surface);--mdc-list-list-item-disabled-state-layer-opacity:0.12;--mdc-list-list-item-label-text-color:var(--sys-on-surface);--mdc-list-list-item-supporting-text-color:var(--sys-on-surface-variant);--mdc-list-list-item-leading-icon-color:var(--sys-on-surface-variant);--mdc-list-list-item-trailing-supporting-text-color:var(--sys-on-surface-variant);--mdc-list-list-item-trailing-icon-color:var(--sys-on-surface-variant);--mdc-list-list-item-selected-trailing-icon-color:var(--sys-primary);--mdc-list-list-item-disabled-label-text-color:var(--sys-on-surface);--mdc-list-list-item-disabled-leading-icon-color:var(--sys-on-surface);--mdc-list-list-item-disabled-trailing-icon-color:var(--sys-on-surface);--mdc-list-list-item-hover-label-text-color:var(--sys-on-surface);--mdc-list-list-item-focus-label-text-color:var(--sys-on-surface);--mdc-list-list-item-hover-state-layer-color:var(--sys-on-surface);--mdc-list-list-item-hover-state-layer-opacity:0.08;--mdc-list-list-item-focus-state-layer-color:var(--sys-on-surface);--mdc-list-list-item-focus-state-layer-opacity:0.12;--mdc-list-list-item-label-text-font:var(--sys-body-large-font);--mdc-list-list-item-label-text-line-height:var(--sys-body-large-line-height);--mdc-list-list-item-label-text-size:var(--sys-body-large-size);--mdc-list-list-item-label-text-tracking:var(--sys-body-large-tracking);--mdc-list-list-item-label-text-weight:var(--sys-body-large-weight);--mdc-list-list-item-supporting-text-font:var(--sys-body-medium-font);--mdc-list-list-item-supporting-text-line-height:var(--sys-body-medium-line-height);--mdc-list-list-item-supporting-text-size:var(--sys-body-medium-size);--mdc-list-list-item-supporting-text-tracking:var(--sys-body-medium-tracking);--mdc-list-list-item-supporting-text-weight:var(--sys-body-medium-weight);--mdc-list-list-item-trailing-supporting-text-font:var(--sys-label-small-font);--mdc-list-list-item-trailing-supporting-text-line-height:var(--sys-label-small-line-height);--mdc-list-list-item-trailing-supporting-text-size:var(--sys-label-small-size);--mdc-list-list-item-trailing-supporting-text-tracking:var(--sys-label-small-tracking);--mdc-list-list-item-trailing-supporting-text-weight:var(--sys-label-small-weight);--mdc-list-list-item-one-line-container-height:48px;--mdc-list-list-item-two-line-container-height:64px;--mdc-list-list-item-three-line-container-height:88px;--mdc-list-list-item-container-shape:0px;--mdc-list-list-item-leading-avatar-shape:9999px;--mdc-list-list-item-leading-icon-size:24px;--mdc-list-list-item-leading-avatar-size:40px;--mdc-list-list-item-trailing-icon-size:24px;--mdc-list-list-item-disabled-label-text-opacity:0.3;--mdc-list-list-item-disabled-leading-icon-opacity:0.38;--mdc-list-list-item-disabled-trailing-icon-opacity:0.38;--mat-list-active-indicator-color:var(--sys-secondary-container);--mat-list-list-item-leading-icon-start-space:16px;--mat-list-list-item-leading-icon-end-space:16px;--mat-list-active-indicator-shape:9999px;--mat-paginator-container-text-color:var(--sys-on-surface);--mat-paginator-container-background-color:var(--sys-surface);--mat-paginator-enabled-icon-color:var(--sys-on-surface-variant);--mat-paginator-disabled-icon-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mat-paginator-container-text-font:var(--sys-body-small-font);--mat-paginator-container-text-line-height:var(--sys-body-small-line-height);--mat-paginator-container-text-size:var(--sys-body-small-size);--mat-paginator-container-text-tracking:var(--sys-body-small-tracking);--mat-paginator-container-text-weight:var(--sys-body-small-weight);--mat-paginator-select-trigger-text-size:var(--sys-body-small-size);--mat-paginator-container-size:56px;--mat-paginator-form-field-container-height:40px;--mat-paginator-form-field-container-vertical-padding:8px;--mat-paginator-touch-target-display:block;--mdc-secondary-navigation-tab-container-height:48px;--mdc-tab-indicator-active-indicator-color:var(--sys-primary);--mdc-tab-indicator-active-indicator-height:2px;--mdc-tab-indicator-active-indicator-shape:0;--mat-tab-header-divider-color:var(--sys-surface-variant);--mat-tab-header-pagination-icon-color:var(--sys-on-surface);--mat-tab-header-inactive-label-text-color:var(--sys-on-surface);--mat-tab-header-active-label-text-color:var(--sys-on-surface);--mat-tab-header-active-ripple-color:var(--sys-on-surface);--mat-tab-header-inactive-ripple-color:var(--sys-on-surface);--mat-tab-header-inactive-focus-label-text-color:var(--sys-on-surface);--mat-tab-header-inactive-hover-label-text-color:var(--sys-on-surface);--mat-tab-header-active-focus-label-text-color:var(--sys-on-surface);--mat-tab-header-active-hover-label-text-color:var(--sys-on-surface);--mat-tab-header-active-focus-indicator-color:var(--sys-primary);--mat-tab-header-active-hover-indicator-color:var(--sys-primary);--mat-tab-header-label-text-font:var(--sys-title-small-font);--mat-tab-header-label-text-size:var(--sys-title-small-size);--mat-tab-header-label-text-tracking:var(--sys-title-small-tracking);--mat-tab-header-label-text-line-height:var(--sys-title-small-line-height);--mat-tab-header-label-text-weight:var(--sys-title-small-weight);--mat-tab-header-divider-height:1px;--mdc-checkbox-disabled-selected-checkmark-color:var(--sys-surface);--mdc-checkbox-selected-focus-state-layer-opacity:0.12;--mdc-checkbox-selected-hover-state-layer-opacity:0.08;--mdc-checkbox-selected-pressed-state-layer-opacity:0.12;--mdc-checkbox-unselected-focus-state-layer-opacity:0.12;--mdc-checkbox-unselected-hover-state-layer-opacity:0.08;--mdc-checkbox-unselected-pressed-state-layer-opacity:0.12;--mdc-checkbox-disabled-selected-icon-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mdc-checkbox-disabled-unselected-icon-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mdc-checkbox-selected-checkmark-color:var(--sys-on-primary);--mdc-checkbox-selected-focus-icon-color:var(--sys-primary);--mdc-checkbox-selected-hover-icon-color:var(--sys-primary);--mdc-checkbox-selected-icon-color:var(--sys-primary);--mdc-checkbox-unselected-focus-icon-color:var(--sys-on-surface);--mdc-checkbox-unselected-hover-icon-color:var(--sys-on-surface);--mdc-checkbox-unselected-icon-color:var(--sys-on-surface-variant);--mdc-checkbox-selected-focus-state-layer-color:var(--sys-primary);--mdc-checkbox-selected-hover-state-layer-color:var(--sys-primary);--mdc-checkbox-selected-pressed-state-layer-color:var(--sys-on-surface);--mdc-checkbox-unselected-focus-state-layer-color:var(--sys-on-surface);--mdc-checkbox-unselected-hover-state-layer-color:var(--sys-on-surface);--mdc-checkbox-unselected-pressed-state-layer-color:var(--sys-primary);--mdc-checkbox-state-layer-size:40px;--mat-checkbox-disabled-label-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mat-checkbox-label-text-color:var(--sys-on-surface);--mat-checkbox-label-text-font:var(--sys-body-medium-font);--mat-checkbox-label-text-line-height:var(--sys-body-medium-line-height);--mat-checkbox-label-text-size:var(--sys-body-medium-size);--mat-checkbox-label-text-tracking:var(--sys-body-medium-tracking);--mat-checkbox-label-text-weight:var(--sys-body-medium-weight);--mat-checkbox-touch-target-display:block;--mdc-text-button-label-text-color:var(--sys-primary);--mdc-text-button-disabled-label-text-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mdc-text-button-label-text-font:var(--sys-label-large-font);--mdc-text-button-label-text-size:var(--sys-label-large-size);--mdc-text-button-label-text-tracking:var(--sys-label-large-tracking);--mdc-text-button-label-text-weight:var(--sys-label-large-weight);--mdc-text-button-container-height:40px;--mdc-text-button-container-shape:9999px;--mdc-protected-button-container-color:var(--sys-surface);--mdc-protected-button-label-text-color:var(--sys-primary);--mdc-protected-button-disabled-container-color:color-mix(in srgb, var(--sys-on-surface) 12%, transparent);--mdc-protected-button-disabled-label-text-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mdc-protected-button-container-elevation-shadow:0px 2px 1px -1px rgba(0, 0, 0, 0.2),0px 1px 1px 0px rgba(0, 0, 0, 0.14),0px 1px 3px 0px rgba(0, 0, 0, 0.12);--mdc-protected-button-disabled-container-elevation-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2),0px 0px 0px 0px rgba(0, 0, 0, 0.14),0px 0px 0px 0px rgba(0, 0, 0, 0.12);--mdc-protected-button-focus-container-elevation-shadow:0px 2px 1px -1px rgba(0, 0, 0, 0.2),0px 1px 1px 0px rgba(0, 0, 0, 0.14),0px 1px 3px 0px rgba(0, 0, 0, 0.12);--mdc-protected-button-hover-container-elevation-shadow:0px 3px 3px -2px rgba(0, 0, 0, 0.2),0px 3px 4px 0px rgba(0, 0, 0, 0.14),0px 1px 8px 0px rgba(0, 0, 0, 0.12);--mdc-protected-button-pressed-container-elevation-shadow:0px 2px 1px -1px rgba(0, 0, 0, 0.2),0px 1px 1px 0px rgba(0, 0, 0, 0.14),0px 1px 3px 0px rgba(0, 0, 0, 0.12);--mdc-protected-button-container-shadow-color:#000000;--mdc-protected-button-label-text-font:var(--sys-label-large-font);--mdc-protected-button-label-text-size:var(--sys-label-large-size);--mdc-protected-button-label-text-tracking:var(--sys-label-large-tracking);--mdc-protected-button-label-text-weight:var(--sys-label-large-weight);--mdc-protected-button-container-height:40px;--mdc-protected-button-container-shape:9999px;--mdc-filled-button-container-color:var(--sys-primary);--mdc-filled-button-label-text-color:var(--sys-on-primary);--mdc-filled-button-disabled-container-color:color-mix(in srgb, var(--sys-on-surface) 12%, transparent);--mdc-filled-button-disabled-label-text-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mdc-filled-button-label-text-font:var(--sys-label-large-font);--mdc-filled-button-label-text-size:var(--sys-label-large-size);--mdc-filled-button-label-text-tracking:var(--sys-label-large-tracking);--mdc-filled-button-label-text-weight:var(--sys-label-large-weight);--mdc-filled-button-container-height:40px;--mdc-filled-button-container-shape:9999px;--mdc-outlined-button-disabled-outline-color:color-mix(in srgb, var(--sys-on-surface) 12%, transparent);--mdc-outlined-button-disabled-label-text-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mdc-outlined-button-label-text-color:var(--sys-primary);--mdc-outlined-button-outline-color:var(--sys-outline);--mdc-outlined-button-label-text-font:var(--sys-label-large-font);--mdc-outlined-button-label-text-size:var(--sys-label-large-size);--mdc-outlined-button-label-text-tracking:var(--sys-label-large-tracking);--mdc-outlined-button-label-text-weight:var(--sys-label-large-weight);--mdc-outlined-button-container-height:40px;--mdc-outlined-button-outline-width:1px;--mdc-outlined-button-container-shape:9999px;--mat-text-button-state-layer-color:var(--sys-primary);--mat-text-button-disabled-state-layer-color:var(--sys-on-surface-variant);--mat-text-button-ripple-color:color-mix(in srgb, var(--sys-primary) 12%, transparent);--mat-text-button-hover-state-layer-opacity:0.08;--mat-text-button-focus-state-layer-opacity:0.12;--mat-text-button-pressed-state-layer-opacity:0.12;--mat-text-button-touch-target-display:block;--mat-text-button-horizontal-padding:12px;--mat-text-button-with-icon-horizontal-padding:16px;--mat-text-button-icon-spacing:8px;--mat-text-button-icon-offset:-4px;--mat-protected-button-state-layer-color:var(--sys-primary);--mat-protected-button-disabled-state-layer-color:var(--sys-on-surface-variant);--mat-protected-button-ripple-color:color-mix(in srgb, var(--sys-primary) 12%, transparent);--mat-protected-button-hover-state-layer-opacity:0.08;--mat-protected-button-focus-state-layer-opacity:0.12;--mat-protected-button-pressed-state-layer-opacity:0.12;--mat-protected-button-touch-target-display:block;--mat-protected-button-horizontal-padding:24px;--mat-protected-button-icon-spacing:8px;--mat-protected-button-icon-offset:-8px;--mat-filled-button-state-layer-color:var(--sys-on-primary);--mat-filled-button-disabled-state-layer-color:var(--sys-on-surface-variant);--mat-filled-button-ripple-color:color-mix(in srgb, var(--sys-on-primary) 12%, transparent);--mat-filled-button-hover-state-layer-opacity:0.08;--mat-filled-button-focus-state-layer-opacity:0.12;--mat-filled-button-pressed-state-layer-opacity:0.12;--mat-filled-button-touch-target-display:block;--mat-filled-button-horizontal-padding:24px;--mat-filled-button-icon-spacing:8px;--mat-filled-button-icon-offset:-8px;--mat-outlined-button-state-layer-color:var(--sys-primary);--mat-outlined-button-disabled-state-layer-color:var(--sys-on-surface-variant);--mat-outlined-button-ripple-color:color-mix(in srgb, var(--sys-primary) 12%, transparent);--mat-outlined-button-hover-state-layer-opacity:0.08;--mat-outlined-button-focus-state-layer-opacity:0.12;--mat-outlined-button-pressed-state-layer-opacity:0.12;--mat-outlined-button-touch-target-display:block;--mat-outlined-button-horizontal-padding:24px;--mat-outlined-button-icon-spacing:8px;--mat-outlined-button-icon-offset:-8px;--mdc-icon-button-icon-color:var(--sys-on-surface-variant);--mdc-icon-button-disabled-icon-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mdc-icon-button-state-layer-size:40px;--mdc-icon-button-icon-size:24px;--mat-icon-button-state-layer-color:var(--sys-on-surface-variant);--mat-icon-button-disabled-state-layer-color:var(--sys-on-surface-variant);--mat-icon-button-ripple-color:color-mix(in srgb, var(--sys-on-surface-variant) 12%, transparent);--mat-icon-button-hover-state-layer-opacity:0.08;--mat-icon-button-focus-state-layer-opacity:0.12;--mat-icon-button-pressed-state-layer-opacity:0.12;--mat-icon-button-touch-target-display:block;--mdc-extended-fab-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2),0px 6px 10px 0px rgba(0, 0, 0, 0.14),0px 1px 18px 0px rgba(0, 0, 0, 0.12);--mdc-extended-fab-focus-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2),0px 6px 10px 0px rgba(0, 0, 0, 0.14),0px 1px 18px 0px rgba(0, 0, 0, 0.12);--mdc-extended-fab-hover-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2),0px 8px 10px 1px rgba(0, 0, 0, 0.14),0px 3px 14px 2px rgba(0, 0, 0, 0.12);--mdc-extended-fab-pressed-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2),0px 6px 10px 0px rgba(0, 0, 0, 0.14),0px 1px 18px 0px rgba(0, 0, 0, 0.12);--mdc-extended-fab-container-shadow-color:#000000;--mdc-extended-fab-label-text-font:var(--sys-label-large-font);--mdc-extended-fab-label-text-size:var(--sys-label-large-size);--mdc-extended-fab-label-text-tracking:var(--sys-label-large-tracking);--mdc-extended-fab-label-text-weight:var(--sys-label-large-weight);--mdc-extended-fab-container-height:56px;--mdc-extended-fab-container-shape:16px;--mdc-fab-container-color:var(--sys-primary-container);--mdc-fab-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2),0px 6px 10px 0px rgba(0, 0, 0, 0.14),0px 1px 18px 0px rgba(0, 0, 0, 0.12);--mdc-fab-focus-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2),0px 6px 10px 0px rgba(0, 0, 0, 0.14),0px 1px 18px 0px rgba(0, 0, 0, 0.12);--mdc-fab-hover-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2),0px 8px 10px 1px rgba(0, 0, 0, 0.14),0px 3px 14px 2px rgba(0, 0, 0, 0.12);--mdc-fab-pressed-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2),0px 6px 10px 0px rgba(0, 0, 0, 0.14),0px 1px 18px 0px rgba(0, 0, 0, 0.12);--mdc-fab-container-shadow-color:#000000;--mdc-fab-container-shape:16px;--mdc-fab-icon-size:24px;--mdc-fab-small-container-color:var(--sys-primary-container);--mdc-fab-small-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2),0px 6px 10px 0px rgba(0, 0, 0, 0.14),0px 1px 18px 0px rgba(0, 0, 0, 0.12);--mdc-fab-small-focus-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2),0px 6px 10px 0px rgba(0, 0, 0, 0.14),0px 1px 18px 0px rgba(0, 0, 0, 0.12);--mdc-fab-small-hover-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2),0px 8px 10px 1px rgba(0, 0, 0, 0.14),0px 3px 14px 2px rgba(0, 0, 0, 0.12);--mdc-fab-small-pressed-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2),0px 6px 10px 0px rgba(0, 0, 0, 0.14),0px 1px 18px 0px rgba(0, 0, 0, 0.12);--mdc-fab-small-container-shadow-color:#000000;--mdc-fab-small-container-shape:12px;--mdc-fab-small-icon-size:24px;--mat-fab-foreground-color:var(--sys-on-primary-container);--mat-fab-state-layer-color:var(--sys-on-primary-container);--mat-fab-ripple-color:color-mix(in srgb, var(--sys-on-primary-container) 12%, transparent);--mat-fab-hover-state-layer-opacity:0.08;--mat-fab-focus-state-layer-opacity:0.12;--mat-fab-pressed-state-layer-opacity:0.12;--mat-fab-disabled-state-container-color:color-mix(in srgb, var(--sys-on-surface) 12%, transparent);--mat-fab-disabled-state-foreground-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mat-fab-touch-target-display:block;--mat-fab-small-foreground-color:var(--sys-on-primary-container);--mat-fab-small-state-layer-color:var(--sys-on-primary-container);--mat-fab-small-ripple-color:color-mix(in srgb, var(--sys-on-primary-container) 12%, transparent);--mat-fab-small-hover-state-layer-opacity:0.08;--mat-fab-small-focus-state-layer-opacity:0.12;--mat-fab-small-pressed-state-layer-opacity:0.12;--mat-fab-small-disabled-state-container-color:color-mix(in srgb, var(--sys-on-surface) 12%, transparent);--mat-fab-small-disabled-state-foreground-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mdc-snackbar-container-color:var(--sys-inverse-surface);--mdc-snackbar-supporting-text-color:var(--sys-inverse-on-surface);--mdc-snackbar-supporting-text-font:var(--sys-body-medium-font);--mdc-snackbar-supporting-text-line-height:var(--sys-body-medium-line-height);--mdc-snackbar-supporting-text-size:var(--sys-body-medium-size);--mdc-snackbar-supporting-text-weight:var(--sys-body-medium-weight);--mdc-snackbar-container-shape:4px;--mat-snack-bar-button-color:var(--sys-inverse-primary);--mat-table-background-color:var(--sys-surface);--mat-table-header-headline-color:var(--sys-on-surface);--mat-table-row-item-label-text-color:var(--sys-on-surface);--mat-table-row-item-outline-color:var(--sys-outline-variant);--mat-table-header-headline-font:var(--sys-title-small-font);--mat-table-header-headline-line-height:var(--sys-title-small-line-height);--mat-table-header-headline-size:var(--sys-title-small-size);--mat-table-header-headline-weight:var(--sys-title-small-weight);--mat-table-header-headline-tracking:var(--sys-title-small-tracking);--mat-table-row-item-label-text-font:var(--sys-body-medium-font);--mat-table-row-item-label-text-line-height:var(--sys-body-medium-line-height);--mat-table-row-item-label-text-size:var(--sys-body-medium-size);--mat-table-row-item-label-text-weight:var(--sys-body-medium-weight);--mat-table-row-item-label-text-tracking:var(--sys-body-medium-tracking);--mat-table-footer-supporting-text-font:var(--sys-body-medium-font);--mat-table-footer-supporting-text-line-height:var(--sys-body-medium-line-height);--mat-table-footer-supporting-text-size:var(--sys-body-medium-size);--mat-table-footer-supporting-text-weight:var(--sys-body-medium-weight);--mat-table-footer-supporting-text-tracking:var(--sys-body-medium-tracking);--mat-table-header-container-height:56px;--mat-table-footer-container-height:52px;--mat-table-row-item-container-height:52px;--mat-table-row-item-outline-width:1px;--mdc-circular-progress-active-indicator-color:var(--sys-primary);--mdc-circular-progress-active-indicator-width:4px;--mdc-circular-progress-size:48px;--mat-badge-background-color:var(--sys-error);--mat-badge-text-color:var(--sys-on-error);--mat-badge-disabled-state-background-color:color-mix(in srgb, var(--sys-error) 38%, transparent);--mat-badge-disabled-state-text-color:var(--sys-on-error);--mat-badge-text-font:var(--sys-label-small-font);--mat-badge-text-size:var(--sys-label-small-size);--mat-badge-text-weight:var(--sys-label-small-weight);--mat-badge-large-size-text-size:var(--sys-label-small-size);--mat-badge-container-shape:9999px;--mat-badge-container-size:16px;--mat-badge-small-size-container-size:6px;--mat-badge-large-size-container-size:16px;--mat-badge-legacy-container-size:unset;--mat-badge-legacy-small-size-container-size:unset;--mat-badge-legacy-large-size-container-size:unset;--mat-badge-container-offset:-12px 0;--mat-badge-small-size-container-offset:-6px 0;--mat-badge-large-size-container-offset:-12px 0;--mat-badge-container-overlap-offset:-12px;--mat-badge-small-size-container-overlap-offset:-6px;--mat-badge-large-size-container-overlap-offset:-12px;--mat-badge-container-padding:0 4px;--mat-badge-small-size-container-padding:0;--mat-badge-large-size-container-padding:0 4px;--mat-badge-small-size-text-size:0;--mat-bottom-sheet-container-text-color:var(--sys-on-surface);--mat-bottom-sheet-container-background-color:var(--sys-surface-container-low);--mat-bottom-sheet-container-text-font:var(--sys-body-large-font);--mat-bottom-sheet-container-text-line-height:var(--sys-body-large-line-height);--mat-bottom-sheet-container-text-size:var(--sys-body-large-size);--mat-bottom-sheet-container-text-tracking:var(--sys-body-large-tracking);--mat-bottom-sheet-container-text-weight:var(--sys-body-large-weight);--mat-bottom-sheet-container-shape:28px;--mat-standard-button-toggle-hover-state-layer-opacity:0.08;--mat-standard-button-toggle-focus-state-layer-opacity:0.12;--mat-standard-button-toggle-text-color:var(--sys-on-surface);--mat-standard-button-toggle-state-layer-color:var(--sys-on-surface);--mat-standard-button-toggle-selected-state-background-color:var(--sys-secondary-container);--mat-standard-button-toggle-selected-state-text-color:var(--sys-on-secondary-container);--mat-standard-button-toggle-disabled-state-text-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mat-standard-button-toggle-disabled-selected-state-text-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mat-standard-button-toggle-disabled-selected-state-background-color:color-mix(in srgb, var(--sys-on-surface) 12%, transparent);--mat-standard-button-toggle-divider-color:var(--sys-outline);--mat-standard-button-toggle-label-text-font:var(--sys-label-large-font);--mat-standard-button-toggle-label-text-line-height:var(--sys-label-large-line-height);--mat-standard-button-toggle-label-text-size:var(--sys-label-large-size);--mat-standard-button-toggle-label-text-tracking:var(--sys-label-large-tracking);--mat-standard-button-toggle-label-text-weight:var(--sys-label-large-weight);--mat-standard-button-toggle-height:40px;--mat-standard-button-toggle-shape:9999px;--mat-standard-button-toggle-background-color:transparent;--mat-standard-button-toggle-disabled-state-background-color:transparent;--mat-datepicker-calendar-date-selected-state-text-color:var(--sys-on-primary);--mat-datepicker-calendar-date-selected-state-background-color:var(--sys-primary);--mat-datepicker-calendar-date-selected-disabled-state-background-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mat-datepicker-calendar-date-today-selected-state-outline-color:var(--sys-primary);--mat-datepicker-calendar-date-focus-state-background-color:color-mix(in srgb, var(--sys-on-surface) 12%, transparent);--mat-datepicker-calendar-date-hover-state-background-color:color-mix(in srgb, var(--sys-on-surface) 8%, transparent);--mat-datepicker-toggle-active-state-icon-color:var(--sys-on-surface-variant);--mat-datepicker-calendar-date-in-range-state-background-color:var(--sys-primary-container);--mat-datepicker-calendar-date-in-comparison-range-state-background-color:var(--sys-tertiary-container);--mat-datepicker-calendar-date-in-overlap-range-state-background-color:var(--sys-secondary-container);--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color:var(--sys-secondary);--mat-datepicker-toggle-icon-color:var(--sys-on-surface-variant);--mat-datepicker-calendar-body-label-text-color:var(--sys-on-surface);--mat-datepicker-calendar-period-button-text-color:var(--sys-on-surface-variant);--mat-datepicker-calendar-period-button-icon-color:var(--sys-on-surface-variant);--mat-datepicker-calendar-navigation-button-icon-color:var(--sys-on-surface-variant);--mat-datepicker-calendar-header-text-color:var(--sys-on-surface-variant);--mat-datepicker-calendar-date-today-outline-color:var(--sys-primary);--mat-datepicker-calendar-date-today-disabled-state-outline-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mat-datepicker-calendar-date-text-color:var(--sys-on-surface);--mat-datepicker-calendar-date-disabled-state-text-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mat-datepicker-calendar-date-preview-state-outline-color:var(--sys-primary);--mat-datepicker-range-input-separator-color:var(--sys-on-surface);--mat-datepicker-range-input-disabled-state-separator-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mat-datepicker-range-input-disabled-state-text-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mat-datepicker-calendar-container-background-color:var(--sys-surface-container-high);--mat-datepicker-calendar-container-text-color:var(--sys-on-surface);--mat-datepicker-calendar-text-font:var(--sys-body-large-font);--mat-datepicker-calendar-text-size:var(--sys-body-large-size);--mat-datepicker-calendar-body-label-text-size:var(--sys-title-small-size);--mat-datepicker-calendar-body-label-text-weight:var(--sys-title-small-weight);--mat-datepicker-calendar-period-button-text-size:var(--sys-title-small-size);--mat-datepicker-calendar-period-button-text-weight:var(--sys-title-small-weight);--mat-datepicker-calendar-header-text-size:var(--sys-title-small-size);--mat-datepicker-calendar-header-text-weight:var(--sys-title-small-weight);--mat-datepicker-calendar-container-shape:16px;--mat-datepicker-calendar-container-touch-shape:28px;--mat-datepicker-calendar-container-elevation-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2),0px 0px 0px 0px rgba(0, 0, 0, 0.14),0px 0px 0px 0px rgba(0, 0, 0, 0.12);--mat-datepicker-calendar-container-touch-elevation-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2),0px 0px 0px 0px rgba(0, 0, 0, 0.14),0px 0px 0px 0px rgba(0, 0, 0, 0.12);--mat-datepicker-calendar-header-divider-color:transparent;--mat-datepicker-calendar-date-outline-color:transparent;--mat-divider-color:var(--sys-outline-variant);--mat-divider-width:1px;--mat-expansion-container-background-color:var(--sys-surface);--mat-expansion-container-text-color:var(--sys-on-surface);--mat-expansion-actions-divider-color:var(--sys-outline-variant);--mat-expansion-header-hover-state-layer-color:color-mix(in srgb, var(--sys-on-surface) 8%, transparent);--mat-expansion-header-focus-state-layer-color:color-mix(in srgb, var(--sys-on-surface) 12%, transparent);--mat-expansion-header-disabled-state-text-color:color-mix(in srgb, var(--sys-on-surface) 38%, transparent);--mat-expansion-header-text-color:var(--sys-on-surface);--mat-expansion-header-description-color:var(--sys-on-surface-variant);--mat-expansion-header-indicator-color:var(--sys-on-surface-variant);--mat-expansion-header-text-font:var(--sys-title-medium-font);--mat-expansion-header-text-size:var(--sys-title-medium-size);--mat-expansion-header-text-weight:var(--sys-title-medium-weight);--mat-expansion-header-text-line-height:var(--sys-title-medium-line-height);--mat-expansion-header-text-tracking:var(--sys-title-medium-tracking);--mat-expansion-container-text-font:var(--sys-body-large-font);--mat-expansion-container-text-line-height:var(--sys-body-large-line-height);--mat-expansion-container-text-size:var(--sys-body-large-size);--mat-expansion-container-text-tracking:var(--sys-body-large-tracking);--mat-expansion-container-text-weight:var(--sys-body-large-weight);--mat-expansion-header-collapsed-state-height:48px;--mat-expansion-header-expanded-state-height:64px;--mat-expansion-container-shape:12px;--mat-expansion-legacy-header-indicator-display:none;--mat-expansion-header-indicator-display:inline-block;--mat-grid-list-tile-header-primary-text-size:var(--sys-body-large);--mat-grid-list-tile-header-secondary-text-size:var(--sys-body-medium);--mat-grid-list-tile-footer-primary-text-size:var(--sys-body-large);--mat-grid-list-tile-footer-secondary-text-size:var(--sys-body-medium);--mat-icon-color:inherit;--mat-sidenav-container-background-color:var(--sys-surface);--mat-sidenav-container-text-color:var(--sys-on-surface-variant);--mat-sidenav-content-background-color:var(--sys-background);--mat-sidenav-content-text-color:var(--sys-on-background);--mat-sidenav-scrim-color:rgba(45, 48, 56, 0.4);--mat-sidenav-container-shape:16px;--mat-sidenav-container-elevation-shadow:none;--mat-sidenav-container-width:360px;--mat-sidenav-container-divider-color:transparent;--mat-stepper-header-icon-foreground-color:var(--sys-surface);--mat-stepper-header-selected-state-icon-background-color:var(--sys-primary);--mat-stepper-header-selected-state-icon-foreground-color:var(--sys-on-primary);--mat-stepper-header-edit-state-icon-background-color:var(--sys-primary);--mat-stepper-header-edit-state-icon-foreground-color:var(--sys-on-primary);--mat-stepper-container-color:var(--sys-surface);--mat-stepper-line-color:var(--sys-outline-variant);--mat-stepper-header-hover-state-layer-color:color-mix(in srgb, var(--sys-inverse-surface) 8%, transparent);--mat-stepper-header-focus-state-layer-color:color-mix(in srgb, var(--sys-inverse-surface) 12%, transparent);--mat-stepper-header-label-text-color:var(--sys-on-surface-variant);--mat-stepper-header-optional-label-text-color:var(--sys-on-surface-variant);--mat-stepper-header-selected-state-label-text-color:var(--sys-on-surface-variant);--mat-stepper-header-error-state-label-text-color:var(--sys-error);--mat-stepper-header-icon-background-color:var(--sys-on-surface-variant);--mat-stepper-header-error-state-icon-foreground-color:var(--sys-error);--mat-stepper-container-text-font:var(--sys-body-medium-font);--mat-stepper-header-label-text-font:var(--sys-title-small-font);--mat-stepper-header-label-text-size:var(--sys-title-small-size);--mat-stepper-header-label-text-weight:var(--sys-title-small-weight);--mat-stepper-header-error-state-label-text-size:var(--sys-title-small-size);--mat-stepper-header-selected-state-label-text-size:var(--sys-title-small-size);--mat-stepper-header-selected-state-label-text-weight:var(--sys-title-small-weight);--mat-stepper-header-height:72px;--mat-stepper-header-focus-state-layer-shape:12px;--mat-stepper-header-hover-state-layer-shape:12px;--mat-stepper-header-error-state-icon-background-color:transparent;--mat-sort-arrow-color:var(--sys-on-surface);--mat-toolbar-container-background-color:var(--sys-surface);--mat-toolbar-container-text-color:var(--sys-on-surface);--mat-toolbar-title-text-font:var(--sys-title-large-font);--mat-toolbar-title-text-line-height:var(--sys-title-large-line-height);--mat-toolbar-title-text-size:var(--sys-title-large-size);--mat-toolbar-title-text-tracking:var(--sys-title-large-tracking);--mat-toolbar-title-text-weight:var(--sys-title-large-weight);--mat-toolbar-standard-height:64px;--mat-toolbar-mobile-height:56px;--mat-tree-container-background-color:var(--sys-surface);--mat-tree-node-text-color:var(--sys-on-surface);--mat-tree-node-text-font:var(--sys-body-large-font);--mat-tree-node-text-size:var(--sys-body-large-size);--mat-tree-node-text-weight:var(--sys-body-large-weight);--mat-tree-node-min-height:48px;--sys-background:#fdfbff;--sys-error:#ba1a1a;--sys-error-container:#ffdad6;--sys-inverse-on-surface:#f2f0f4;--sys-inverse-primary:#abc7ff;--sys-inverse-surface:#2f3033;--sys-on-background:#1a1b1f;--sys-on-error:#ffffff;--sys-on-error-container:#410002;--sys-on-primary:#ffffff;--sys-on-primary-container:#001b3f;--sys-on-primary-fixed:#001b3f;--sys-on-primary-fixed-variant:#00458f;--sys-on-secondary:#ffffff;--sys-on-secondary-container:#131c2b;--sys-on-secondary-fixed:#131c2b;--sys-on-secondary-fixed-variant:#3e4759;--sys-on-surface:#1a1b1f;--sys-on-surface-variant:#44474e;--sys-on-tertiary:#ffffff;--sys-on-tertiary-container:#00006e;--sys-on-tertiary-fixed:#00006e;--sys-on-tertiary-fixed-variant:#0000ef;--sys-outline:#74777f;--sys-outline-variant:#c4c6d0;--sys-primary:#005cbb;--sys-primary-container:#d7e3ff;--sys-primary-fixed:#d7e3ff;--sys-primary-fixed-dim:#abc7ff;--sys-scrim:#000000;--sys-secondary:#565e71;--sys-secondary-container:#dae2f9;--sys-secondary-fixed:#dae2f9;--sys-secondary-fixed-dim:#bec6dc;--sys-shadow:#000000;--sys-surface:#fdfbff;--sys-surface-bright:#faf9fd;--sys-surface-container:white;--sys-surface-container-high:white;--sys-surface-container-highest:#e3e2e6;--sys-surface-container-low:white;--sys-surface-container-lowest:#ffffff;--sys-surface-dim:rgb(218.6, 217.6, 221.6);--sys-surface-tint:#005cbb;--sys-surface-variant:#e0e2ec;--sys-tertiary:#343dff;--sys-tertiary-container:#e0e0ff;--sys-tertiary-fixed:#e0e0ff;--sys-tertiary-fixed-dim:#bec2ff;--sys-body-large:400 1rem/1.5rem Roboto,sans-serif;--sys-body-large-font:Roboto,sans-serif;--sys-body-large-line-height:1.5rem;--sys-body-large-size:1rem;--sys-body-large-tracking:0.031rem;--sys-body-large-weight:400;--sys-body-medium:400 0.875rem/1.25rem Roboto,sans-serif;--sys-body-medium-font:Roboto,sans-serif;--sys-body-medium-line-height:1.25rem;--sys-body-medium-size:0.875rem;--sys-body-medium-tracking:0.016rem;--sys-body-medium-weight:400;--sys-body-small:400 0.75rem/1rem Roboto,sans-serif;--sys-body-small-font:Roboto,sans-serif;--sys-body-small-line-height:1rem;--sys-body-small-size:0.75rem;--sys-body-small-tracking:0.025rem;--sys-body-small-weight:400;--sys-display-large:400 3.562rem/4rem Roboto,sans-serif;--sys-display-large-font:Roboto,sans-serif;--sys-display-large-line-height:4rem;--sys-display-large-size:3.562rem;--sys-display-large-tracking:-0.016rem;--sys-display-large-weight:400;--sys-display-medium:400 2.812rem/3.25rem Roboto,sans-serif;--sys-display-medium-font:Roboto,sans-serif;--sys-display-medium-line-height:3.25rem;--sys-display-medium-size:2.812rem;--sys-display-medium-tracking:0rem;--sys-display-medium-weight:400;--sys-display-small:400 2.25rem/2.75rem Roboto,sans-serif;--sys-display-small-font:Roboto,sans-serif;--sys-display-small-line-height:2.75rem;--sys-display-small-size:2.25rem;--sys-display-small-tracking:0rem;--sys-display-small-weight:400;--sys-headline-large:400 2rem/2.5rem Roboto,sans-serif;--sys-headline-large-font:Roboto,sans-serif;--sys-headline-large-line-height:2.5rem;--sys-headline-large-size:2rem;--sys-headline-large-tracking:0rem;--sys-headline-large-weight:400;--sys-headline-medium:400 1.75rem/2.25rem Roboto,sans-serif;--sys-headline-medium-font:Roboto,sans-serif;--sys-headline-medium-line-height:2.25rem;--sys-headline-medium-size:1.75rem;--sys-headline-medium-tracking:0rem;--sys-headline-medium-weight:400;--sys-headline-small:400 1.5rem/2rem Roboto,sans-serif;--sys-headline-small-font:Roboto,sans-serif;--sys-headline-small-line-height:2rem;--sys-headline-small-size:1.5rem;--sys-headline-small-tracking:0rem;--sys-headline-small-weight:400;--sys-label-large:500 0.875rem/1.25rem Roboto,sans-serif;--sys-label-large-font:Roboto,sans-serif;--sys-label-large-line-height:1.25rem;--sys-label-large-size:0.875rem;--sys-label-large-tracking:0.006rem;--sys-label-large-weight:500;--sys-label-large-weight-prominent:700;--sys-label-medium:500 0.75rem/1rem Roboto,sans-serif;--sys-label-medium-font:Roboto,sans-serif;--sys-label-medium-line-height:1rem;--sys-label-medium-size:0.75rem;--sys-label-medium-tracking:0.031rem;--sys-label-medium-weight:500;--sys-label-medium-weight-prominent:700;--sys-label-small:500 0.688rem/1rem Roboto,sans-serif;--sys-label-small-font:Roboto,sans-serif;--sys-label-small-line-height:1rem;--sys-label-small-size:0.688rem;--sys-label-small-tracking:0.031rem;--sys-label-small-weight:500;--sys-title-large:400 1.375rem/1.75rem Roboto,sans-serif;--sys-title-large-font:Roboto,sans-serif;--sys-title-large-line-height:1.75rem;--sys-title-large-size:1.375rem;--sys-title-large-tracking:0rem;--sys-title-large-weight:400;--sys-title-medium:500 1rem/1.5rem Roboto,sans-serif;--sys-title-medium-font:Roboto,sans-serif;--sys-title-medium-line-height:1.5rem;--sys-title-medium-size:1rem;--sys-title-medium-tracking:0.009rem;--sys-title-medium-weight:500;--sys-title-small:500 0.875rem/1.25rem Roboto,sans-serif;--sys-title-small-font:Roboto,sans-serif;--sys-title-small-line-height:1.25rem;--sys-title-small-size:0.875rem;--sys-title-small-tracking:0.006rem;--sys-title-small-weight:500;--mat-dialog-container-max-width:90vw}.mat-theme-loaded-marker{display:none}.mat-display-large,.mat-typography .mat-display-large,.mat-typography h1{font:var(--sys-display-large);letter-spacing:var(--sys-display-large-tracking);margin:0 0 .5em}.mat-display-medium,.mat-typography .mat-display-medium,.mat-typography h2{font:var(--sys-display-medium);letter-spacing:var(--sys-display-medium-tracking);margin:0 0 .5em}.mat-display-small,.mat-typography .mat-display-small,.mat-typography h3{font:var(--sys-display-small);letter-spacing:var(--sys-display-small-tracking);margin:0 0 .5em}.mat-headline-large,.mat-typography .mat-headline-large,.mat-typography h4{font:var(--sys-headline-large);letter-spacing:var(--sys-headline-large-tracking);margin:0 0 .5em}.mat-headline-medium,.mat-typography .mat-headline-medium,.mat-typography h5{font:var(--sys-headline-medium);letter-spacing:var(--sys-headline-medium-tracking);margin:0 0 .5em}.mat-headline-small,.mat-typography .mat-headline-small,.mat-typography h6{font:var(--sys-headline-small);letter-spacing:var(--sys-headline-small-tracking);margin:0 0 .5em}.mat-title-large,.mat-typography .mat-title-large{font:var(--sys-title-large);letter-spacing:var(--sys-title-large-tracking)}.mat-title-medium,.mat-typography .mat-title-medium{font:var(--sys-title-medium);letter-spacing:var(--sys-title-medium-tracking)}.mat-title-small,.mat-typography .mat-title-small{font:var(--sys-title-small);letter-spacing:var(--sys-title-small-tracking)}.mat-body-large,.mat-typography,.mat-typography .mat-body-large{font:var(--sys-body-large);letter-spacing:var(--sys-body-large-tracking)}.mat-body-large p,.mat-typography .mat-body-large p,.mat-typography p{margin:0 0 .75em}.mat-body-medium,.mat-typography .mat-body-medium{font:var(--sys-body-medium);letter-spacing:var(--sys-body-medium-tracking)}.mat-body-small,.mat-typography .mat-body-small{font:var(--sys-body-small);letter-spacing:var(--sys-body-small-tracking)}.mat-label-large,.mat-typography .mat-label-large{font:var(--sys-label-large);letter-spacing:var(--sys-label-large-tracking)}.mat-label-medium,.mat-typography .mat-label-medium{font:var(--sys-label-medium);letter-spacing:var(--sys-label-medium-tracking)}.mat-label-small,.mat-typography .mat-label-small{font:var(--sys-label-small);letter-spacing:var(--sys-label-small-tracking)}.mat-mdc-raised-button.mat-primary{--mdc-protected-button-container-color:var(--sys-primary);--mdc-protected-button-label-text-color:white;--mat-protected-button-state-layer-color:white;--mat-protected-button-ripple-color:rgba(255, 255, 255, 0.1)}.mat-mdc-raised-button.mat-warn{--mdc-protected-button-container-color:var(--sys-error);--mdc-protected-button-label-text-color:white;--mat-protected-button-state-layer-color:white;--mat-protected-button-ripple-color:rgba(255, 255, 255, 0.1)}ecui-sidenav-layout{--mat-sidenav-container-shape:0px;--mat-sidenav-container-width:auto}.mat-datepicker-content{box-shadow:1px 1px 1px silver!important}