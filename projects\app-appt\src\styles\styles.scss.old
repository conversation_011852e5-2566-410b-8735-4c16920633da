// /* ----------------------------------------------------------------------------------------------------- */
// /*  @ Import/write your custom styles here.
// /*  @ Styles from this file will override 'vendors.scss' and Fuse's base styles.
// /* ----------------------------------------------------------------------------------------------------- */

// regular style toast
@import 'ngx-toastr/toastr';
// @import "~intl-tel-input/build/css/intlTelInput.css";
// @import "~bootstrap/dist/css/bootstrap.min.css";
::ng-deep .mat-dialog-container {
    border-radius: 8px; 
    overflow: hidden; 
  } 

body {
	font-family: Roboto, Helvetica, Arial, sans-serif;
}

ng-component{
 display:block;
 width:100%;
}
.wrapper {
	margin-bottom: 20px;
}

/*****************************************************
	Custom styling example bellow.
*****************************************************/

.iti {
	display: block !important;
	margin-bottom: 20px;
}

.iti .dropdown-menu.country-dropdown {
	border-top-left-radius: 0px;
	border-top-right-radius: 0px;
	border-color: #c7cace;
	margin-top: -1px;
}

.iti .iti__country-list {
	box-shadow: none;
	font-size: 14px;
	margin-left: 0;
	width: 244px;
	max-height: 170px;
}

.iti__flag-container.open + input {
	border-bottom-left-radius: 0px;
	border-bottom-right-radius: 0px;
}

.iti .search-container input {
	font-size: 14px;
	border-color: #c7cace;
	border-radius: 0;
	padding: 5px 10px;
}

.iti .search-container input:focus {
	outline: none;
}

@media screen and (max-width: 479px) {
	.iti .iti__country-list {
		width: 88.3vw;
	}
}

ngx-intl-tel-input input {
	width: 100%;
	height: 54px;
	margin-bottom: 20px;
	padding: 10px;
	border-style: solid;
	border-width: 1px;
	border-color: #c7cace;
	border-radius: 4px;
	font-size: 18px;
}

ngx-intl-tel-input.ng-invalid.ng-touched input {
	border: 1px solid #c0392b;
}

ngx-intl-tel-input input:hover {
	box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.24);
}

ngx-intl-tel-input input:focus {
	outline: none !important;
	border-color: #3498db;
	box-shadow: 0 0 0 0 #000;
}

ngx-intl-tel-input input::-webkit-input-placeholder {
	color: #bac2c7;
}

ngx-intl-tel-input input:-ms-input-placeholder {
	color: #bac2c7;
}

ngx-intl-tel-input input::-ms-input-placeholder {
	color: #bac2c7;
}

ngx-intl-tel-input input::placeholder {
	color: #bac2c7;
}

ngx-intl-tel-input input[disabled] {
	background-color: #e5eaf1;
}
