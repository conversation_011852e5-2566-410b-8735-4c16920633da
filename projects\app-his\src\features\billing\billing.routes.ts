import { Routes } from '@angular/router';
import { SearchBillingsComponent, ViewBillingComponent, ViewTypeComponent } from './components';
import { EditServiceComponent } from '@app-his/features/visit-services';
import { AppNavigationState } from 'ec-ngcore';

export default [
    {
        path: '',
        title: 'Billing Queue',
        component: SearchBillingsComponent,
    },
    {
        path: ':id',
        title: 'Billing Details',
        data: { appNavigationState: AppNavigationState.TransactionMode },
        component: ViewBillingComponent
    },
    {
        path: 'edit/:id',
        title: 'Edit Service',
        data: { appNavigationState: AppNavigationState.TransactionMode },
        component: EditServiceComponent
    },
    {
        path: 'view/:id',
        title: 'Billing Form',
        data: { appNavigationState: AppNavigationState.TransactionMode },
        component: ViewTypeComponent
    }
] as Routes;
