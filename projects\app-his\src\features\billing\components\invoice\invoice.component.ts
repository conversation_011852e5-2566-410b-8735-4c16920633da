import { CommonModule, DatePipe, formatDate } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { MaterialPackage } from '@app-his/utils';
import { CommonService} from 'his-components';
import { InvoiceListComponent, SearchInvoiceComponent } from '@app-his/components';
import { BillingService } from 'ecmed-api/billing';
import { ApplicationService, dateUtils } from 'ec-ngcore';
import { SearchLayoutComponent } from 'ec-ngcore/ui';

@Component({
  standalone: true,
  imports: [SearchLayoutComponent, CommonModule, MaterialPackage, 
            SearchInvoiceComponent, InvoiceListComponent],
  templateUrl: './invoice.component.html',
  styleUrl: './invoice.component.scss',
  providers:[DatePipe]
})
export class InvoiceComponent {
  fromDate:Date|null=null;
  toDate:Date|null=null;
  constructor(
    public commonServices: CommonService,
    public _dialog: MatDialog,
    public router: Router,
    public appService: ApplicationService,
    public API: BillingService) {
    this.toDate = new Date();
    this.fromDate =dateUtils.addMonths(this.toDate,-1);
  }

  invoicedata: any;
  visitData: any;
  endDate: any = "";
  startDate: any = ""

  ngOnInit() {
    let date = new Date();
    let toDate = new Date();
    this.endDate = toDate;
    let fromDate = new Date(date.setDate(date.getDate() - 30));
    this.startDate = fromDate;
    const formattedFromDate = formatDate(this.startDate, 'yyyy-MM-dd', 'en-US');
    const formattedToDate = formatDate(this.endDate, 'yyyy-MM-dd', 'en-US');
    let objData = {
      invNo: null,
      visitno: null,
      FromDate: formattedFromDate,
      Todate: formattedToDate
    };
    this.getinvoice(objData);
  }

  public handleFetchVisitData() {
    this.commonServices.fetchVisitData(['ID']).subscribe(
      (response: any) => {
        this.visitData = response;
      }
    );
  }

  getinvoice(invoice:any) {
    if (invoice?.FromDate && invoice?.Todate) {
      this.API.billingSearchInvoiceGet({
        fromDate:invoice.FromDate,
        todate:invoice.Todate,
        invNo:invoice.invNo,
        visitno:invoice.visitno}
      ).subscribe(result => {
        if (result?.length) {
          this.invoicedata = result;
        }
      });
    } else {
      this.appService.alertDialog({
        'title': 'Warning Error',
        message: 'DATE is Required.'
      });
    }
  }

  getviewbilling(result:string) {
    this.router.navigate(['Search_Billing/invoice/' + result]);
  }

  resettable() {
    this.toDate = new Date();
    this.fromDate =dateUtils.addMonths(this.toDate,-1);

    this.invoicedata = [];
  }
}
