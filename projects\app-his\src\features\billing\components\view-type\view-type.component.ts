import { CommonModule, DatePipe, DecimalPipe } from '@angular/common';
import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MaterialPackage } from '@app-his/utils';
import { BillingFinalizeViewComponent } from '@app-his/components';
import { BillingService, OrderService } from 'ecmed-api/billing';
import { FillHeightDirective } from 'ec-ngcore/ui';

@Component({
  standalone: true,
  imports: [CommonModule, MaterialPackage, FillHeightDirective, BillingFinalizeViewComponent],
  templateUrl: './view-type.component.html',
  styleUrl: './view-type.component.scss',
  providers: [DatePipe, DecimalPipe,
  ],
})
export class ViewTypeComponent {
  constructor(
    public router: Router,
    public route: ActivatedRoute,
    public serviceApi: OrderService,
    public API: BillingService
  ) { }

  serviceList: any[] = [];
  receiptList: any[] = [];
  invoiceDetails: any[] = [];
  payerDetails: any[] = [];

  ngOnInit() {
    this.handleInvoiceDetails();
  }

  handleBack() {
    if (window.location.href.includes("Search_Billing/invoice")) {
      this.router.navigate(["Search_Billing"]);
    } else if (window.location.href.includes("billings/view")) {
      this.router.navigate(["billings"]);
    }
  }

  handlePrint() {
    const printContent = document.getElementById('print-section');
    const WindowPrt = window.open('', '', 'left=0,top=0,width=800,height=900,toolbar=0,scrollbars=0,status=0');

    if (WindowPrt && printContent) {
      WindowPrt.document.write(`
        <html>
          <head>
            <title>Invoice - ${this.invoiceDetails[0]?.DOCNO || 'Invoice'}</title>
            <style>
              @page {
                size: A4;
                margin: 10mm;
              }
              body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 0;
                color: #000;
              }
              .print-container {
                width: 100%;
                max-width: 210mm;
                margin: 0 auto;
                padding: 10px;
              }
              table {
                width: 100%;
                border-collapse: collapse;
                font-size: 12px;
                margin-bottom: 15px;
              }
              th, td {
                padding: 6px 8px;
                border: 1px solid #ddd;
              }
              .text-right {
                text-align: right;
              }
              .no-print {
                display: none;
              }
            </style>
          </head>
          <body>
            <div class="print-container">
              ${printContent.innerHTML}
            </div>
            <script>
              setTimeout(function() {
                window.print();
                window.close();
              }, 200);
            </script>
          </body>
        </html>
      `);
      WindowPrt.document.close();
    }
  }

  handleEdit() {
    const patientID = this.route.snapshot.params['id'];
    if (patientID) {
      this.router.navigate(["/billings/", parseInt(patientID)]);
    }
  }

  handleInvoiceDetails() {
    const id = this.route.snapshot.params['id'];
    if (!id) return;

    this.API.billingGetInvoiceDetailsGet({id:id}).subscribe({
      next: (res) => {
        if (res?.length) {
          this.invoiceDetails = res;
          this.handleVisitDetailsApi(this.invoiceDetails[0]?.IDENTIFIER2);
          this.handleGetReceiptsApi(this.invoiceDetails[0]?.IDENTIFIER2);
          this.handlePayerDetails();
        }
      },
      error: (err) => console.error('Error loading invoice details:', err)
    });
  }

  handleVisitDetailsApi(id: any) {
    if (!id) return;

    this.serviceApi.orderGetVisitDetailsGet({visitId:id}).subscribe({
      next: (res) => {
        if (res?.length) {
          this.serviceList = res;
        }
      },
      error: (err) => console.error('Error loading visit details:', err)
    });
  }

  handleGetReceiptsApi(id: any) {
    if (!id) return;

    this.API.billingGetPaymentDetailsGet({id:id}).subscribe({
      next: (res) => {
        if (res?.length) {
          this.receiptList = res;
        }
      },
      error: (err) => console.error('Error loading receipts:', err)
    });
  }

  handlePayerDetails() {
    if (!this.invoiceDetails?.length) return;

    const patID = this.invoiceDetails[0].PATIENTID;
    this.API.billingGetPayerDetailssGet(patID).subscribe({
      next: (res) => {
        if (res?.length) {
          this.payerDetails = res;
        }
      },
      error: (err) => console.error('Error loading payer details:', err)
    });
  }
}

