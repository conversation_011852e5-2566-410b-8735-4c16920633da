<!-- Modern Counter Operator Dashboard -->
<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
  <div class="max-w-7xl mx-auto">

    <!-- Header Section -->
    <div class="flex justify-between items-center mb-8">
      <div class="flex items-center space-x-3">
        <div class="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center">
          <mat-icon class="text-white text-2xl" svgIcon="mat_outline:price_change"></mat-icon>
        </div>
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Counter Operator</h1>
          <p class="text-gray-600">Manage your counter sessions and operations</p>
        </div>
      </div>

      <button
        class="flex items-center space-x-2 bg-white hover:bg-gray-50 text-gray-700 px-6 py-3 rounded-xl shadow-md transition-all duration-200 hover:shadow-lg border border-gray-200"
        (click)="print()">
        <mat-icon class="text-blue-600" svgIcon="mat_solid:print"></mat-icon>
        <span class="font-medium">Print Report</span>
      </button>
    </div>

    <!-- Counter Information Card -->
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 mb-8 overflow-hidden">
      <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-8 py-6">
        <div class="flex items-center space-x-3">
          <mat-icon class="text-white text-2xl" svgIcon="mat_outline:price_change"></mat-icon>
          <h2 class="text-2xl font-bold text-white">Counter Information</h2>
        </div>
      </div>

      <div class="p-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Counter Details -->
          <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-800">Counter</h3>
              <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <mat-icon class="text-blue-600" svgIcon="mat_outline:desktop_windows"></mat-icon>
              </div>
            </div>
            <p class="text-2xl font-bold text-blue-700">{{counterDes}}</p>
          </div>

          <!-- Date -->
          <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-800">Date</h3>
              <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <mat-icon class="text-green-600" svgIcon="mat_outline:calendar_today"></mat-icon>
              </div>
            </div>
            <p class="text-2xl font-bold text-green-700">{{todayDate | date: "dd-MM-yyyy"}}</p>
          </div>

          <!-- Status -->
          <div class="bg-gradient-to-br from-purple-50 to-violet-50 rounded-xl p-6 border border-purple-100">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-800">Status</h3>
              <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <mat-icon class="text-purple-600" svgIcon="mat_outline:info"></mat-icon>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 rounded-full"
                   [ngClass]="counterStatus === 'Open' ? 'bg-green-500' : 'bg-red-500'"></div>
              <p class="text-2xl font-bold"
                 [ngClass]="counterStatus === 'Open' ? 'text-green-700' : 'text-red-700'">{{counterStatus}}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Session Information Card -->
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 mb-8 overflow-hidden">
      <div class="bg-gradient-to-r from-indigo-600 to-purple-700 px-8 py-6">
        <div class="flex items-center space-x-3">
          <mat-icon class="text-white text-2xl" svgIcon="mat_outline:people"></mat-icon>
          <h2 class="text-2xl font-bold text-white">Session Information</h2>
        </div>
      </div>

      <div class="p-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- Login Count -->
          <div class="bg-gradient-to-br from-orange-50 to-amber-50 rounded-xl p-6 border border-orange-100">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-800">Login Count</h3>
              <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <mat-icon class="text-orange-600" svgIcon="mat_outline:login"></mat-icon>
              </div>
            </div>
            <p class="text-3xl font-bold text-orange-700">{{loginCount}}</p>
          </div>

          <!-- Session Count -->
          <div class="bg-gradient-to-br from-cyan-50 to-blue-50 rounded-xl p-6 border border-cyan-100">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-800">Session Count</h3>
              <div class="w-10 h-10 bg-cyan-100 rounded-lg flex items-center justify-center">
                <mat-icon class="text-cyan-600" svgIcon="mat_outline:schedule"></mat-icon>
              </div>
            </div>
            <p class="text-3xl font-bold text-cyan-700">{{sessionCount}}</p>
          </div>

          <!-- Session Status -->
          <div class="bg-gradient-to-br from-rose-50 to-pink-50 rounded-xl p-6 border border-rose-100">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-800">Session Status</h3>
              <div class="w-10 h-10 bg-rose-100 rounded-lg flex items-center justify-center">
                <mat-icon class="text-rose-600" svgIcon="mat_outline:info"></mat-icon>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 rounded-full"
                   [ngClass]="sessionStatus === 'Open' ? 'bg-green-500' : 'bg-red-500'"></div>
              <p class="text-2xl font-bold"
                 [ngClass]="sessionStatus === 'Open' ? 'text-green-700' : 'text-red-700'">{{sessionStatus}}</p>
            </div>
          </div>
        </div>

        <!-- Time and Amount Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
          <!-- Start Time -->
          <div class="bg-gray-50 rounded-xl p-6 border border-gray-200">
            <h4 class="text-sm font-medium text-gray-600 mb-2">Start Time</h4>
            <p class="text-xl font-bold text-gray-800">{{startTime || 'Not Started'}}</p>
          </div>

          <!-- End Time -->
          <div class="bg-gray-50 rounded-xl p-6 border border-gray-200">
            <h4 class="text-sm font-medium text-gray-600 mb-2">End Time</h4>
            <p class="text-xl font-bold text-gray-800">{{endTime || 'Not Ended'}}</p>
          </div>

          <!-- Float Amount -->
          <div class="bg-gray-50 rounded-xl p-6 border border-gray-200">
            <h4 class="text-sm font-medium text-gray-600 mb-2">Float Amount</h4>
            <p class="text-xl font-bold text-green-600">${{floatAmount || '0.00'}}</p>
          </div>

          <!-- Opening Cash -->
          <div class="bg-gray-50 rounded-xl p-6 border border-gray-200">
            <h4 class="text-sm font-medium text-gray-600 mb-2">Opening Cash</h4>
            <p class="text-xl font-bold text-green-600">${{openingAmount || '0.00'}}</p>
          </div>
        </div>

        <!-- User Information -->
        <div class="mt-6 bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 border border-gray-200">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
              <mat-icon class="text-gray-600" svgIcon="mat_outline:person"></mat-icon>
            </div>
            <div>
              <h4 class="text-sm font-medium text-gray-600">Current User</h4>
              <p class="text-lg font-semibold text-gray-800">{{counterDes}}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-wrap gap-4 justify-center">
      <button
        class="flex items-center space-x-3 px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
        [ngClass]="{
          'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white': !disableCounter,
          'bg-gray-300 text-gray-500 cursor-not-allowed': disableCounter
        }"
        [disabled]="disableCounter"
        (click)="openCounter()">
        <mat-icon svgIcon="mat_outline:lock_open"></mat-icon>
        <span>{{counterButton}}</span>
      </button>

      <button
        class="flex items-center space-x-3 px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
        [ngClass]="{
          'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white': !disableSession,
          'bg-gray-300 text-gray-500 cursor-not-allowed': disableSession
        }"
        [disabled]="disableSession"
        (click)="session()">
        <mat-icon svgIcon="mat_outline:play_arrow"></mat-icon>
        <span>{{sessionButton}}</span>
      </button>

      <button
        class="flex items-center space-x-3 px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
        [ngClass]="{
          'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white': !disableConfirmActual,
          'bg-gray-300 text-gray-500 cursor-not-allowed': disableConfirmActual
        }"
        [disabled]="disableConfirmActual"
        (click)="confirmActuals()">
        <mat-icon svgIcon="mat_outline:check_circle"></mat-icon>
        <span>Confirm Actuals</span>
      </button>
    </div>
  </div>
</div>
