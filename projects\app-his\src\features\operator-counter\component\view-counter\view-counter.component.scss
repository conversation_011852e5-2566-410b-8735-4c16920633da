/* Modern Counter Operator Styles */

/* Custom animations and transitions */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Component specific styles */
.counter-card {
  animation: fadeInUp 0.6s ease-out;
}

.status-indicator {
  animation: pulse 2s infinite;
}

/* Button hover effects */
button:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Card hover effects */
.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* Gradient text effects */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Print styles */
@media print {
  body * {
    visibility: hidden;
  }

  .print-section, .print-section * {
    visibility: visible;
  }

  .print-section {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 0;
    margin: 0;
  }

  /* Hide action buttons in print */
  button {
    display: none !important;
  }

  /* Improve print layout */
  .bg-white {
    background-color: white !important;
    box-shadow: none !important;
  }

  /* Remove gradients for print */
  .bg-gradient-to-r,
  .bg-gradient-to-br {
    background: white !important;
  }
}

/* Responsive design improvements */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
  }

  .flex-wrap {
    flex-direction: column;
  }

  button {
    width: 100%;
    justify-content: center;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}