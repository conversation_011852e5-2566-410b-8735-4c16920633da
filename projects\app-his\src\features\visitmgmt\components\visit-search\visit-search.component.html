<ecui-search-layout> 
    <ng-container ecFixedContent>    
        <div class="pt-2 bg-card">
            <app-visit-searchform #form [enableAdd]="true"
            [enableSearchPatient]="true" 
            [fromDate]="fromDate" [toDate]="toDate" 
            (onReset)="onResetRequested()" (onNew)="onWalkin()"
            (onSearch)="visitSearch()" >
        </app-visit-searchform >
        </div>
    </ng-container>
 
    <ng-container ecScrollableContent>    
        <div class="border-1">
               
            <app-visit-searchlist (onSelect)="view($event)" [dataSource]="dataSource">
            </app-visit-searchlist>
        </div>
    </ng-container>
</ecui-search-layout>
