import { DatePipe, formatDate } from '@angular/common';
import { Component, Input, OnChanges, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { VisitSearchFormComponent, VisitSearchListComponent } from '@app-his/components';
import { SearchLayoutComponent } from 'ec-ngcore/ui';
import { ApplicationService, dateUtils } from 'ec-ngcore';
import { CommonService } from 'his-components';
@Component({
  standalone: true,
  imports: [SearchLayoutComponent, VisitSearchFormComponent, VisitSearchListComponent],
  templateUrl: './visit-search.component.html',
  styleUrl: './visit-search.component.scss'
})

export class VisitMgmtSearchVisitComponent implements OnInit {
  dataSource:any [] =[];
  fromDate:Date|null=null;
  toDate:Date|null=null;
  @ViewChild(VisitSearchFormComponent, { static: true }) form?: VisitSearchFormComponent;

  constructor(
    public router: Router,
    private _appService: ApplicationService,
    public commonServices: CommonService
  ) {
     const dt= new Date();
     this.toDate = dt;
     this.fromDate = dateUtils.addMonths(dt,-1);
  }


  ngOnInit() {
    this.visitSearch();
  }

  visitSearch() {
    this.form?.searchVisits().subscribe({
      next: (data: []) => {
        this.dataSource = data !== null ? data : [];
        if (this.dataSource.length == 0) {
          this._appService.alertDialog({
            'title': 'System Error',
            message: 'No Recorde Found'
          });
        }
      }
    });
  }

  onResetRequested() {
    this.dataSource = [];
  }

  view(id: any) {
    console.log(id?.IDENTIFIER,'visit view')
    const visitId = id?.IDENTIFIER.toString();
    this.router.navigate(['visits/view/' + visitId]);
  }
  onWalkin(){
    // this.router.navigate(['visits/new'])
  }

}
