import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { AppointmentsAPIService } from 'ecmed-api/appointments';
import { PatientService, VisitService } from 'ecmed-api/visitmgmt';
import { MaterialPackage } from '@app-his/utils';
import { VisitViewComponent } from 'his-components';
import { FillHeightDirective } from 'ec-ngcore/ui';
import { BillingService } from 'ecmed-api/billing';

@Component({
  selector: 'app-visitpatient-view',
  standalone: true,
  imports: [VisitViewComponent, MaterialPackage,  FillHeightDirective],
  templateUrl: './visit-view.component.html',
  styleUrl: './visit-view.component.scss'
})
export class VisitMgmtViewVisitComponent implements OnInit {
  patient: any;
  visit: any;
  appList: any;
  id: any;
  appoinmentId?: string;
  visitId?: string;
  isPrintClicked: boolean = false;
  payerDetails: any = []

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    public _HISAPI: AppointmentsAPIService,
    public _API: VisitService,
    public billingApi: BillingService
  ) { }

  ngOnInit() {
    this.route.params.subscribe(params => {
      const id = params['id'];
      this.id = id;
      this.getvisitviewid();
    });
  }

  onPrint() {
    this.isPrintClicked = true;
    window.print();
  }

  public getvisitviewid() {
    this._API.visitGetVisitAppointmentDetailsGet({id:this.id}).subscribe(
      (result: any) => {
        if (result.length) {
          this.visit = result;
          this.handlePayerDetails(result[0].PAT_IDENTIFIER)
        }
      }
    );
  }

  public handlePayerDetails(id:any) {

    console.log(id)
    this.billingApi.billingGetPayerDetailssGet({id:id}).subscribe({
      next: (res) => {
        if (res?.length) {
          this.payerDetails = res
        }
      }
    })

  }

  goback() {
    this.router.navigate(['visits'])
  }

}
